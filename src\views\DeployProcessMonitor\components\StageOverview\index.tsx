import React from 'react';
import { CaretUpOutlined, CaretDownOutlined, BarChartOutlined } from '@ant-design/icons';
import { stageConfig } from '../../utils/constant';
import './index.scss';

interface StageData {
  [key: string]: number;
}

interface StageOverviewProps {
  title: string;
  todayData: StageData;
  compareData: StageData;
  isProgress?: boolean;
  activeStages?: string[];
  onStageClick?: (stageKey: string) => void;
}

const StageOverview: React.FC<StageOverviewProps> = ({
  title,
  todayData,
  compareData,
  isProgress = true,
  activeStages = [],
  onStageClick,
}) => {
  const getStageBackgroundImage = (index: number, isActive: boolean) => {
    const totalStages = stageConfig.reduce((acc, category) => acc + category.stages.length, 0);
    
    if (index === 0) {
      return isActive 
        ? require('@/assets/image/deployProcess/indicator-left-active.png')
        : require('@/assets/image/deployProcess/indicator-left.png');
    } else if (index === totalStages - 1) {
      return isActive
        ? require('@/assets/image/deployProcess/indicator-right-active.png')
        : require('@/assets/image/deployProcess/indicator-right.png');
    } else {
      return isActive
        ? require('@/assets/image/deployProcess/indicator-middle-active.png')
        : require('@/assets/image/deployProcess/indicator-middle.png');
    }
  };

  const renderChangeIndicator = (current: number, compare: number) => {
    const change = current - compare;
    if (change > 0) {
      return (
        <div className="change-indicator positive">
          <CaretUpOutlined />
          <span>环比+{change}</span>
        </div>
      );
    } else if (change < 0) {
      return (
        <div className="change-indicator negative">
          <CaretDownOutlined />
          <span>环比{change}</span>
        </div>
      );
    } else {
      return (
        <div className="change-indicator neutral">
          <span>环比持平</span>
        </div>
      );
    }
  };

  let stageIndex = 0;

  return (
    <div className="stage-overview">
      <div className="stage-header">
        <BarChartOutlined className="header-icon" />
        <span className="header-title">{title}</span>
      </div>
      
      {stageConfig.map((category, categoryIndex) => (
        <div key={categoryIndex} className="stage-category">
          <div className="category-header">{category.category}</div>
          <div className="stage-items">
            {category.stages.map((stage) => {
              const currentStageIndex = stageIndex++;
              const isActive = activeStages.includes(stage.key);
              const fieldName = isProgress ? stage.field : stage.durationField;
              const currentValue = todayData[fieldName] || 0;
              const compareValue = compareData[fieldName] || 0;
              
              return (
                <div
                  key={stage.key}
                  className={`stage-item ${isActive ? 'active' : ''} ${isProgress ? 'clickable' : ''}`}
                  style={{
                    backgroundImage: `url(${getStageBackgroundImage(currentStageIndex, isActive)})`,
                  }}
                  onClick={() => isProgress && onStageClick?.(stage.key)}
                >
                  <div className="stage-content">
                    <div className="stage-name">{stage.name}</div>
                    <div className="stage-value">
                      {currentValue}
                      {!isProgress && <span className="unit">天</span>}
                    </div>
                    {renderChangeIndicator(currentValue, compareValue)}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default React.memo(StageOverview);
