import React, { Children } from 'react';
import { Navigate } from 'react-router-dom';
import MainLayout from './layouts/Mainlayout';
import SimpleLayout from './layouts/SimpleLayout';
import Login from './views/Login';
import AsyncComponent from './components/AsyncComponent';
import CommonIframePage from './views/CommonIframePage';
import OrderManagement from './views/OrderManagement';
import InsuranceManage from './views/InsuranceManage';
import InsuranceManageAdd from './views/InsuranceManage/Add';
import InsuranceManageEdit from './views/InsuranceManage/Edit';
import CostAllocationManage from './views/CostAllocationManage';
import DeployMap from './views/DeployMap';
const GridManage = AsyncComponent(
  () => import(/* webpackChunkName: "GridManage" */ './views/GridManage'),
);
const GridManageEdit = AsyncComponent(
  () =>
    import(/* webpackChunkName: "GridManageEdit" */ './views/GridManage/Edit'),
);
const HardwareModel = AsyncComponent(
  () => import(/* webpackChunkName: "HardwareModel" */ './views/HardwareModel'),
);
const HardwareModelEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "HardwareModelEdit" */ './views/HardwareModel/edit'
    ),
);
const SensorSolution = AsyncComponent(
  () =>
    import(/* webpackChunkName: "SensorSolution" */ './views/SensorSolution'),
);
const SensorSolutionEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "SensorSolutionEdit" */ './views/SensorSolution/Edit'
    ),
);
const HardwareType = AsyncComponent(
  () => import(/* webpackChunkName: "HardwareType" */ './views/HardwareType'),
);
const HardwareTypeEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "HardwareTypeEdit" */ './views/HardwareType/Edit'
    ),
);
const VehicleType = AsyncComponent(
  () => import(/* webpackChunkName: "VehicleType" */ './views/VehicleType'),
);
const VehicleTypeEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleTypeEdit" */ './views/VehicleType/Edit'
    ),
);
const DeviceInfo = AsyncComponent(
  () => import(/* webpackChunkName: "DeviceInfo" */ './views/DeviceInfo'),
);
const DeviceInfoEdit = AsyncComponent(
  () =>
    import(/* webpackChunkName: "DeviceInfoEdit" */ './views/DeviceInfo/Edit'),
);
const FactoryManage = AsyncComponent(
  () => import(/* webpackChunkName: "FactoryManage" */ './views/FactoryManage'),
);
const FactoryManageEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "FactoryManageEdit" */ './views/FactoryManage/Edit'
    ),
);
const RepairOrderManagement = AsyncComponent(
  () => import(/* webpackChunkName: "Three" */ './views/RepairOrderManagement'),
);
const StationManagement = AsyncComponent(
  () => import(/* webpackChunkName: "Three" */ './views/StationManagement'),
);
const AddStation = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "AddStation" */ './views/StationManagement/components/AddStation'
    ),
);
const StationDetail = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "StationDetail" */ './views/StationManagement/components/StationDetail'
    ),
);
const VehicleInstruction = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleInstruction" */ './views/VehicleInstruction'
    ),
);
const VehicleInstructionEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleInstructionEdit" */ './views/VehicleInstruction/Edit'
    ),
);
const AbnormalStart = AsyncComponent(
  () => import(/* webpackChunkName: "AbnormalStart" */ './views/AbnormalStart'),
);
const AbnormalStartEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "AbnormalStartEdit" */ './views/AbnormalStart/Edit'
    ),
);
const HardwareSerial = AsyncComponent(
  () =>
    import(/* webpackChunkName: "HardwareSerial" */ './views/HardwareSerial'),
);
const AppOperation = AsyncComponent(
  () => import(/* webpackChunkName: "AppOperation" */ './views/AppOperation'),
);
const MrManage = AsyncComponent(
  () => import(/* webpackChunkName: "MrManage" */ './views/MrManage'),
);
const MrEdit = AsyncComponent(
  () => import(/* webpackChunkName: "MrEdit" */ './views/MrManage/Edit'),
);

const IssueCategoryManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IssueCategoryManage" */ './views/IssueCategoryManage'
    ),
);
const IssueCategoryEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IssueCategoryEdit" */ './views/IssueCategoryManage/Edit'
    ),
);

const IssueLabelManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IssueLabelManage" */ './views/IssueLabelManage'
    ),
);
const IssueLabelEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IssueLabelEdit" */ './views/IssueLabelManage/Edit'
    ),
);

const TestingManage = AsyncComponent(
  () => import(/* webpackChunkName: "TestingManage" */ './views/TestingManage'),
);
const TestingEdit = AsyncComponent(
  () =>
    import(/* webpackChunkName: "TestingEdit" */ './views/TestingManage/Edit'),
);
const ErrorCodeConf = AsyncComponent(
  () => import(/* webpackChunkName: "ErrorCodeConf" */ './views/ErrorCodeConf'),
);
const ErrorCodeConfEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "ErrorCodeConfEdit" */ './views/ErrorCodeConf/Edit'
    ),
);
const RoleManage = AsyncComponent(
  () => import(/* webpackChunkName: "RoleManage" */ './views/RoleManage'),
);
const ResourceManage = AsyncComponent(
  () =>
    import(/* webpackChunkName: "ResourceManage" */ './views/ResourceManage'),
);
const RoleManageEdit = AsyncComponent(
  () =>
    import(/* webpackChunkName: "RoleManageEdit" */ './views/RoleManage/Edit'),
);
const ResourceManageEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "ResourceManageEdit" */ './views/ResourceManage/Edit'
    ),
);

const Cockpitmanagement = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "Cockpitmanagement" */ './views/CockpitManagement'
    ),
);
const CockpitEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "CockpitEdit" */ './views/CockpitManagement/Edit'
    ),
);
const CockpitTeamManagement = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "CockpitTeamManagement" */ './views/CockpitTeamManagement'
    ),
);

const CockpitTeamEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "CockpitTeamEdit" */ './views/CockpitTeamManagement/Edit'
    ),
);
const DeviceLifeCycle = AsyncComponent(
  () => import(/* webpackChunkName: "DeviceLife" */ './views/DeviceLife'),
);

const StopPointEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "StopPointEdit" */ './views/StationManagement/components/StopConfig/Edit'
    ),
);

const BlackList = AsyncComponent(
  () =>
    import(/* webpackChunkName: "ServerBlackList" */ './views/ServerBlackList'),
);

const OperationTime = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "SetOperationTime" */ './views/SetOperationTime'
    ),
);
const VehicleEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleEdit" */ './views/StationManagement/components/LinkedVehicle/VehicleEdit'
    ),
);
const GridEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleEdit" */ './views/StationManagement/components/LinkedVehicle/VehicleEdit/components/GirdEdit'
    ),
);
const BindStopsBatch = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleEdit" */ './views/StationManagement/components/LinkedVehicle/VehicleEdit/components/BindStopsForUsingVehicle'
    ),
);
const XMapInfoManagement = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "XMapManagement" */ './views/XMapsManagement/index'
    ),
);
const XMapVersionManagement = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "XMapManagement" */ './views/XMapsManagement/XMapVersionTable/index'
    ),
);
const IndicatorConfig = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IndicatorConfig" */ './views/IndicatorConfig/index'
    ),
);
const IndicatorLayerConfig = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IndicatorLayerConfig" */ './views/IndicatorLayerConfig/index'
    ),
);
const IssuePoolConfig = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "IssuePoolConfig" */ './views/IssuePoolConfig/index'
    ),
);
const RobotMap = AsyncComponent(
  () => import(/* webpackChunkName: "RobotMap" */ './views/RobotMap/index'),
);
const VersionInfo = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VersionInfo" */ './views/RobotMap/VersionInfo'
    ),
);
const MapInfo = AsyncComponent(
  () => import(/* webpackChunkName: "MapInfo" */ './views/RobotMap/MapInfo'),
);
const TaskManageView = AsyncComponent(
  () =>
    import(/* webpackChunkName: "TaskManageView" */ './views/TaskManage/index'),
);
const TaskDetailView = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TaskDetailView" */ './views/TaskManage/taskDetail'
    ),
);

const WarehouseEdit = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "WarehouseEdit" */ './views/StationManagement/components/LinkedVehicle/WarehouseEdit'
    ),
);

const MaintainAbnormalDuration = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "MaintainAbnormalDuration" */ './views/MaintainAbnormalDuration'
    ),
);
const AddAbnormalDuration = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "AddAbnormalDuration" */ './views/MaintainAbnormalDuration/addAbnormalDuration'
    ),
);
const DeployPlanManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "DeployPlanManage" */ './views/DeployPlanManage'
    ),
);

const TransportModelSiteConfig = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportModelSiteConfig" */ './views/TransportModelSiteConfig'
    ),
);

const TransportModelRecommendResult = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportModelRecommendResult" */ './views/TransportModelRecommendResult'
    ),
);

const TransportDataStop = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportDataStop" */ './views/TransportDataStop'
    ),
);

const TransportDataSchedule = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportDataSchedule" */ './views/TransportDataSchedule'
    ),
);

const TransportDataStation = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportDataStation" */ './views/TransportDataStation'
    ),
);

const TransportDataCity = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportDataCity" */ './views/TransportDataCity'
    ),
);

const TransportDataState = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "TransportDataState" */ './views/TransportDataState'
    ),
);

const ShlefTypeManage = AsyncComponent(
  () =>
    import(/* webpackChunkName: "ShlefTypeManage" */ './views/ShlefTypeManage'),
);

const DeviceShelfTypeManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "DeviceShelfTypeManage" */ './views/DeviceShelfTypeManage'
    ),
);
const ParkingRangeManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "ParkingRangeManage" */ './views/ParkingRangeManage'
    ),
);
const VehicleDemandManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleDemandManage" */ './views/VehicleDemandManage'
    ),
);
const VehicleDemandDetail = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleDemandDetail" */ './views/VehicleDemandManage/VehicleDemandDetail'
    ),
);
const VehicleOrderManage = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleOrderManage" */ './views/VehicleOrderManage'
    ),
);
const ConfigureVehicle = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "ConfigureVehicle" */ './views/VehicleOrderManage/ConfigureVehicle'
    ),
);
const ShippingVehicle = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "ShippingVehicle" */ './views/VehicleOrderManage/ShippingVehicle'
    ),
);
const VehicleAcceptance = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleAcceptance" */ './views/VehicleOrderManage/VehicleAcceptance'
    ),
);
const VehicleOrderDetail = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "VehicleOrderDetail" */ './views/VehicleOrderManage/VehicleOrderDetail'
    ),
);
const MapManage = AsyncComponent(
  () => import(/* webpackChunkName: "MapManage" */ './views/MapManage'),
);
const DeployProcessMonitor = AsyncComponent(
  () =>
    import(
      /* webpackChunkName: "DeployProcessMonitor" */ './views/DeployProcessMonitor'
    ),
);
const baseRoutes: any[] = [
  {
    path: '/',
    element: <SimpleLayout />,
    children: [
      { index: true, element: <Navigate to="login" /> },
      { path: 'login', element: <Login /> },
    ],
  },
  {
    path: '/app',
    element: <MainLayout />,
    children: [
      {
        path: 'deployPlan',
        children: [{ index: true, element: <DeployPlanManage /> }],
      },
      {
        path: 'transportModelSiteConfig',
        children: [{ index: true, element: <TransportModelSiteConfig /> }],
      },
      {
        path: 'transportModelRecommendResult',
        children: [{ index: true, element: <TransportModelRecommendResult /> }],
      },
      {
        path: 'transportDataStop',
        children: [{ index: true, element: <TransportDataStop /> }],
      },
      {
        path: 'transportDataSchedule',
        children: [{ index: true, element: <TransportDataSchedule /> }],
      },
      {
        path: 'transportDataStation',
        children: [{ index: true, element: <TransportDataStation /> }],
      },
      {
        path: 'transportDataCity',
        children: [{ index: true, element: <TransportDataCity /> }],
      },
      {
        path: 'transportDataState',
        children: [{ index: true, element: <TransportDataState /> }],
      },
      {
        path: 'maintainAbnormalDuration',
        children: [
          { index: true, element: <MaintainAbnormalDuration /> },
          {
            path: 'addAbnormalDuration',
            element: <AddAbnormalDuration />,
          },
        ],
      },
      {
        path: 'robotMapManage',
        children: [
          { index: true, element: <RobotMap /> },
          {
            path: 'versionInfo',
            children: [
              { index: true, element: <VersionInfo /> },
              { path: 'mapInfo', element: <MapInfo /> },
            ],
          },
        ],
      },
      {
        path: 'taskManage',
        children: [
          { index: true, element: <TaskManageView /> },
          { path: 'detail', element: <TaskDetailView /> },
        ],
      },
      {
        path: 'roleManage',
        children: [
          { index: true, element: <RoleManage /> },
          { path: 'edit', element: <RoleManageEdit /> },
        ],
      },
      {
        path: 'resourceManage',
        children: [
          { index: true, element: <ResourceManage /> },
          { path: 'edit', element: <ResourceManageEdit /> },
        ],
      },
      {
        path: 'errorCodeConf',
        children: [
          { index: true, element: <ErrorCodeConf /> },
          { path: 'edit', element: <ErrorCodeConfEdit /> },
        ],
      },
      {
        path: 'vehicleInstruction',
        children: [
          { index: true, element: <VehicleInstruction /> },
          { path: 'edit', element: <VehicleInstructionEdit /> },
        ],
      },
      {
        path: 'abnormalStart',
        children: [
          { index: true, element: <AbnormalStart /> },
          { path: 'edit', element: <AbnormalStartEdit /> },
        ],
      },
      {
        path: 'hardwareSerial',
        children: [{ index: true, element: <HardwareSerial /> }],
      },
      {
        path: 'appOperation',
        children: [
          { index: true, element: <AppOperation /> },
          // { path: 'edit', element: <DeviceInfoEdit /> },
        ],
      },
      {
        path: 'mrManage',
        children: [
          { index: true, element: <MrManage /> },
          { path: 'edit', element: <MrEdit /> },
        ],
      },
      {
        path: 'issueCateGoryManage',
        children: [
          { index: true, element: <IssueCategoryManage /> },
          { path: 'edit', element: <IssueCategoryEdit /> },
        ],
      },
      {
        path: 'issueLabelManage',
        children: [
          { index: true, element: <IssueLabelManage /> },
          { path: 'edit', element: <IssueLabelEdit /> },
        ],
      },
      {
        path: 'testing',
        children: [
          { index: true, element: <TestingManage /> },
          { path: 'edit', element: <TestingEdit /> },
        ],
      },
      {
        path: 'gridManage',
        children: [
          { index: true, element: <GridManage /> },
          { path: 'edit', element: <GridManageEdit /> },
        ],
      },
      {
        path: 'hardwareModel',
        children: [
          { index: true, element: <HardwareModel /> },
          { path: 'edit', element: <HardwareModelEdit /> },
        ],
      },
      {
        path: 'sensorSolution',
        children: [
          { index: true, element: <SensorSolution /> },
          { path: 'edit', element: <SensorSolutionEdit /> },
        ],
      },
      {
        path: 'hardwareType',
        children: [
          { index: true, element: <HardwareType /> },
          { path: 'edit', element: <HardwareTypeEdit /> },
        ],
      },
      {
        path: 'factoryManage',
        children: [
          { index: true, element: <FactoryManage /> },
          { path: 'edit', element: <FactoryManageEdit /> },
        ],
      },
      {
        path: 'repairOrderManagement',
        element: <RepairOrderManagement />,
      },
      {
        path: 'deviceInfo',
        children: [
          { index: true, element: <DeviceInfo /> },
          { path: 'edit', element: <DeviceInfoEdit /> },
        ],
      },
      {
        path: 'vehicleType',
        children: [
          { index: true, element: <VehicleType /> },
          { path: 'edit', element: <VehicleTypeEdit /> },
        ],
      },
      {
        path: 'stationManagement',
        children: [
          {
            index: true,
            element: <StationManagement />,
          },
          {
            path: 'addStation',
            element: <AddStation />,
          },
          {
            path: 'detail',
            children: [
              { index: true, element: <StationDetail /> },
              {
                path: 'bindStopPointBacth',
                element: <BindStopsBatch />,
              },
              {
                path: 'vehicleEdit',
                element: <VehicleEdit />,
              },
              {
                path: 'gridEdit',
                element: <GridEdit />,
              },
              {
                path: 'addStopPoint',
                element: <StopPointEdit />,
              },
              {
                path: 'editStopPoint',
                element: <StopPointEdit />,
              },
              {
                path: 'warahouseedit',
                element: <WarehouseEdit />,
              },
            ],
          },
        ],
      },
      {
        path: 'commoniframe',
        children: [{ index: true, element: <CommonIframePage /> }],
      },
      {
        path: 'cockpitmanagement',
        children: [
          { index: true, element: <Cockpitmanagement /> },
          { path: 'add', element: <CockpitEdit /> },
          { path: 'edit/:cockpitNumber', element: <CockpitEdit /> },
        ],
      },
      {
        path: 'cockpitteam',
        children: [
          { index: true, element: <CockpitTeamManagement /> },
          { path: 'add', element: <CockpitTeamEdit /> },
          {
            path: 'edit/:cockpitTeamNumber/:cockpitTeamName',
            element: <CockpitTeamEdit />,
          },
        ],
      },
      {
        path: 'deviceLife',
        children: [{ index: true, element: <DeviceLifeCycle /> }],
      },
      {
        path: 'ordermanagement',
        children: [{ index: true, element: <OrderManagement /> }],
      },
      {
        path: 'blacklist',
        children: [{ index: true, element: <BlackList /> }],
      },
      {
        path: 'operationtime',
        children: [{ index: true, element: <OperationTime /> }],
      },
      {
        path: 'xMapInfoManagement',
        children: [{ index: true, element: <XMapInfoManagement /> }],
      },
      {
        path: 'xMapVersionManagement',
        children: [{ index: true, element: <XMapVersionManagement /> }],
      },
      {
        path: 'indicatorConfig',
        children: [{ index: true, element: <IndicatorConfig /> }],
      },
      {
        path: 'indicatorLayerConfig',
        children: [{ index: true, element: <IndicatorLayerConfig /> }],
      },
      {
        path: 'issuePoolConfig',
        children: [{ index: true, element: <IssuePoolConfig /> }],
      },
      {
        path: 'insuranceManage',
        children: [
          { index: true, element: <InsuranceManage /> },
          { path: 'addInsurance', element: <InsuranceManageAdd /> },
          {
            path: 'editInsurance/:insuranceId',
            element: <InsuranceManageEdit />,
          },
        ],
      },
      {
        path: 'costAllocationManage',
        children: [{ index: true, element: <CostAllocationManage /> }],
      },
      {
        path: 'deployMap',
        children: [{ index: true, element: <DeployMap /> }],
      },
      {
        path: 'shelfTypeManage',
        children: [{ index: true, element: <ShlefTypeManage /> }],
      },
      {
        path: 'deviceShelfTypeManage',
        children: [{ index: true, element: <DeviceShelfTypeManage /> }],
      },
      {
        path: 'stopRangeManage',
        children: [{ index: true, element: <ParkingRangeManage /> }],
      },
      {
        path: 'vehicleDemandManage',
        children: [
          { index: true, element: <VehicleDemandManage /> },
          { path: 'detail', element: <VehicleDemandDetail /> },
        ],
      },
      {
        path: 'vehicleOrderManage',
        children: [
          { index: true, element: <VehicleOrderManage /> },
          { path: 'configureVehicle', element: <ConfigureVehicle /> },
          { path: 'shippingVehicle', element: <ShippingVehicle /> },
          { path: 'vehicleAcceptance', element: <VehicleAcceptance /> },
          { path: 'vehicleOrderDetail', element: <VehicleOrderDetail /> },
        ],
      },
      {
        path: 'mapManage',
        children: [{ index: true, element: <MapManage /> }],
      },
      {
        path: 'deployProcessMonitor',
        children: [{ index: true, element: <DeployProcessMonitor /> }],
      },
    ],
  },
  { path: '/ota/*', element: <MainLayout /> },
];

export default baseRoutes;
