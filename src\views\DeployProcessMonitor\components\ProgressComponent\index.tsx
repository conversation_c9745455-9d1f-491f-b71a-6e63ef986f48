import React, { useState, useEffect, useRef, useMemo } from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { UnorderedListOutlined } from '@ant-design/icons';
import MetricsCard from '../MetricsCard';
import StageOverview from '../StageOverview';
import { deployProcessMonitorApi } from '@/fetch/business/deployProcessMonitor';
import { commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  progressMetricsConfig,
  progressSearchConfig,
} from '../../utils/constant';
import './index.scss';

// 进度表格列配置（基础配置，不包含render函数）
const progressTableColumns = [
  {
    title: '基本信息',
    children: [
      {
        title: '省市区',
        dataIndex: 'location',
        key: 'location',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '省区片区',
        dataIndex: 'area',
        key: 'area',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '站点名称',
        dataIndex: 'stationName',
        key: 'stationName',
        width: 150,
        fixed: 'left',
      },
      {
        title: '站点编号',
        dataIndex: 'stationNumber',
        key: 'stationNumber',
        width: 150,
        fixed: 'left',
        hidden: true,
      },
    ],
  },
  {
    title: '订单信息',
    children: [
      {
        title: '订单编号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: 150,
      },
      {
        title: '车辆数',
        dataIndex: 'orderVehicleCount',
        key: 'orderVehicleCount',
        width: 100,
      },
      {
        title: '需求提报时间',
        dataIndex: 'requirementCreateTime',
        key: 'requirementCreateTime',
        width: 180,
      },
    ],
  },
  {
    title: '部署状态',
    children: [
      {
        title: '需求状态',
        dataIndex: 'requirementStatus',
        key: 'requirementStatus',
        width: 100,
      },
      {
        title: '订单状态',
        dataIndex: 'orderStatus',
        key: 'orderStatus',
        width: 100,
      },
      {
        title: '配车状态',
        dataIndex: 'supplyStatus',
        key: 'supplyStatus',
        width: 100,
      },
      {
        title: '保险状态',
        dataIndex: 'insuranceStatus',
        key: 'insuranceStatus',
        width: 100,
      },
      {
        title: '发车状态',
        dataIndex: 'dispatchStatus',
        key: 'dispatchStatus',
        width: 100,
      },
      {
        title: '到车状态',
        dataIndex: 'receiveStatus',
        key: 'receiveStatus',
        width: 100,
      },
      {
        title: '画图状态',
        dataIndex: 'mapDrawStatus',
        key: 'mapDrawStatus',
        width: 100,
      },
      {
        title: '采图状态',
        dataIndex: 'mapCollectionStatus',
        key: 'mapCollectionStatus',
        width: 100,
      },
      {
        title: '制图状态',
        dataIndex: 'mapMakeStatus',
        key: 'mapMakeStatus',
        width: 100,
      },
      {
        title: '验图状态',
        dataIndex: 'mapCheckStatus',
        key: 'mapCheckStatus',
        width: 100,
      },
      {
        title: '验收状态',
        dataIndex: 'checkOrderStatus',
        key: 'checkOrderStatus',
        width: 100,
      },
      {
        title: '交付状态',
        dataIndex: 'deliveryStatus',
        key: 'deliveryStatus',
        width: 100,
      },
    ],
  },
  {
    title: '时间信息',
    children: [
      {
        title: '实际交付时间',
        dataIndex: 'deliveryCompleteTime',
        key: 'deliveryCompleteTime',
        width: 180,
        fixed: 'right',
      },
      {
        title: '预期交付月份',
        dataIndex: 'expectedDeliveryTime',
        key: 'expectedDeliveryTime',
        width: 150,
        fixed: 'right',
      },
    ],
  },
];

interface ProgressComponentProps {
  filterCondition: any;
  stageData: any;
  onStageDataUpdate: (data: any) => void;
}

const ProgressComponent: React.FC<ProgressComponentProps> = ({
  filterCondition,
  stageData,
  onStageDataUpdate,
}) => {
  const [overviewData, setOverviewData] = useState<any>({});
  const [activeStages, setActiveStages] = useState<string[]>([]);
  const [searchCondition, setSearchCondition] = useState<any>({
    pageNum: 1,
    pageSize: 10,
    searchForm: {},
  });
  const [searchFormConfig, setSearchFormConfig] =
    useState(progressSearchConfig);
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  // 渲染状态的公共方法
  const renderStatus = (value: number) => {
    return value === 1 ? '已完成' : '进行中';
  };

  // 格式化表格列配置
  const formatColumns = useMemo(() => {
    return progressTableColumns.map((group: any) => ({
      ...group,
      children: group.children?.map((col: any) => {
        // 为状态字段添加render函数
        if (col.dataIndex?.includes('Status')) {
          return {
            ...col,
            render: renderStatus,
          };
        }
        return col;
      }),
    }));
  }, []);

  // 使用useTableData钩子
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    (params) =>
      deployProcessMonitorApi.getProgressPageList({
        ...params,
        ...filterCondition,
        deploymentStage:
          activeStages.length > 0 ? activeStages.join(',') : undefined,
      }),
    'progress-table',
    false,
  );

  // 初始化下拉框数据
  useEffect(() => {
    initializeDropdownData();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (filterCondition) {
      loadOverviewData();
    }
  }, [filterCondition]);

  // 监听阶段数据变化
  useEffect(() => {
    if (
      activeStages.length > 0 ||
      Object.keys(searchCondition.searchForm).length > 0
    ) {
      reloadTable();
    }
  }, [activeStages, searchCondition]);

  const initializeDropdownData = async () => {
    try {
      const stationRes = await commonApi.getStationList();
      if (stationRes.code === HttpStatusCode.Success) {
        const newFormConfig = {
          ...searchFormConfig,
          fields: searchFormConfig.fields.map((field: any) => {
            if (field.fieldName === 'stationNumber') {
              return {
                ...field,
                options: stationRes.data || [],
              };
            }
            return field;
          }),
        };
        setSearchFormConfig(newFormConfig);
      }
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
    }
  };

  const loadOverviewData = async () => {
    try {
      const res = await deployProcessMonitorApi.getProgressOverview(
        filterCondition,
      );
      if (res.code === HttpStatusCode.Success) {
        setOverviewData(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('加载进度总览数据失败:', error);
      message.error('加载数据失败');
    }
  };

  const handleStageClick = (stageKey: string) => {
    const newActiveStages = activeStages.includes(stageKey)
      ? activeStages.filter((key) => key !== stageKey)
      : [...activeStages, stageKey];

    setActiveStages(newActiveStages);
  };

  const onSearchClick = (values: any) => {
    const formattedValues = { ...values };

    // 格式化时间字段
    if (
      values.deliveryCompleteTime &&
      values.deliveryCompleteTime.length === 2
    ) {
      formattedValues.deliveryCompleteStartTime =
        values.deliveryCompleteTime[0].format('YYYY-MM-DD') + ' 00:00:00';
      formattedValues.deliveryCompleteEndTime =
        values.deliveryCompleteTime[1].format('YYYY-MM-DD') + ' 23:59:59';
      delete formattedValues.deliveryCompleteTime;
    }

    if (values.expectedDeliveryTime) {
      formattedValues.expectedDeliveryTime =
        values.expectedDeliveryTime.format('YYYY-MM');
    }

    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: formattedValues,
    });
  };

  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: {},
    });
  };

  // 构建指标数据
  const metricsData = progressMetricsConfig.map((config) => ({
    name: config.name,
    count: overviewData[config.field] || 0,
    bgPic: config.bgPic,
  }));

  return (
    <div className="progress-component">
      {/* 指标卡片 */}
      <MetricsCard metrics={metricsData} />

      {/* 阶段总览 */}
      <StageOverview
        title="进度效率总览"
        todayData={stageData?.todayEveryStageStatistic || {}}
        compareData={stageData?.compareEveryStageStatistic || {}}
        isProgress={true}
        activeStages={activeStages}
        onStageClick={handleStageClick}
      />

      {/* 数据明细 */}
      <div className="data-detail">
        <div className="detail-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">数据明细</span>
        </div>

        <div ref={searchRef}>
          <CommonForm
            formConfig={searchFormConfig}
            defaultValue={{
              ...searchCondition,
              ...searchCondition.searchForm,
              fetchStationList: true,
            }}
            layout="inline"
            formType="search"
            getFormInstance={(ref) => (searchFormRef.current = ref)}
            onSearchClick={onSearchClick}
            onResetClick={onResetClick}
          />
        </div>

        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey="progress-detail-table"
          columns={formatColumns}
          loading={loading}
          rowKey="stationBaseId"
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            setSearchCondition(value);
          }}
          showColumnSetting={true}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(ProgressComponent);
