import React, { useState, useEffect, useRef } from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { UnorderedListOutlined } from '@ant-design/icons';
import MetricsCard from '../MetricsCard';
import StageOverview from '../StageOverview';
import { deployProcessMonitorApi } from '@/fetch/business/deployProcessMonitor';
import { commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  progressMetricsConfig,
  progressSearchConfig,
  progressTableColumns,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
} from '../../utils/constant';
import './index.scss';

interface ProgressComponentProps {
  filterCondition: any;
  stageData: any;
  onStageDataUpdate: (data: any) => void;
}

const ProgressComponent: React.FC<ProgressComponentProps> = ({
  filterCondition,
  stageData,
  onStageDataUpdate,
}) => {
  const [overviewData, setOverviewData] = useState<any>({});
  const [activeStages, setActiveStages] = useState<string[]>([]);
  const [searchCondition, setSearchCondition] = useState<any>({
    pageNum: 1,
    pageSize: 10,
    searchForm: {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(progressSearchConfig);
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  // 使用useTableData钩子
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    (params) => deployProcessMonitorApi.getProgressPageList({
      ...params,
      ...filterCondition,
      deploymentStage: activeStages.length > 0 ? activeStages.join(',') : undefined,
    }),
    'progress-table',
    false
  );

  // 初始化下拉框数据
  useEffect(() => {
    initializeDropdownData();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (filterCondition) {
      loadOverviewData();
    }
  }, [filterCondition]);

  // 监听阶段数据变化
  useEffect(() => {
    if (activeStages.length > 0 || Object.keys(searchCondition.searchForm).length > 0) {
      reloadTable();
    }
  }, [activeStages, searchCondition]);

  const initializeDropdownData = async () => {
    try {
      const stationRes = await commonApi.getStationList();
      if (stationRes.code === HttpStatusCode.Success) {
        const newFormConfig = {
          ...searchFormConfig,
          fields: searchFormConfig.fields.map((field: any) => {
            if (field.fieldName === 'stationNumber') {
              return {
                ...field,
                options: stationRes.data || [],
              };
            }
            return field;
          }),
        };
        setSearchFormConfig(newFormConfig);
      }
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
    }
  };

  const loadOverviewData = async () => {
    try {
      const res = await deployProcessMonitorApi.getProgressOverview(filterCondition);
      if (res.code === HttpStatusCode.Success) {
        setOverviewData(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('加载进度总览数据失败:', error);
      message.error('加载数据失败');
    }
  };

  const handleStageClick = (stageKey: string) => {
    const newActiveStages = activeStages.includes(stageKey)
      ? activeStages.filter(key => key !== stageKey)
      : [...activeStages, stageKey];
    
    setActiveStages(newActiveStages);
  };

  const onSearchClick = (values: any) => {
    const formattedValues = { ...values };
    
    // 格式化时间字段
    if (values.deliveryCompleteTime && values.deliveryCompleteTime.length === 2) {
      formattedValues.deliveryCompleteStartTime = values.deliveryCompleteTime[0].format('YYYY-MM-DD') + ' 00:00:00';
      formattedValues.deliveryCompleteEndTime = values.deliveryCompleteTime[1].format('YYYY-MM-DD') + ' 23:59:59';
      delete formattedValues.deliveryCompleteTime;
    }
    
    if (values.expectedDeliveryTime) {
      formattedValues.expectedDeliveryTime = values.expectedDeliveryTime.format('YYYY-MM');
    }

    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: formattedValues,
    });
  };

  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: {},
    });
  };

  // 构建指标数据
  const metricsData = progressMetricsConfig.map(config => ({
    name: config.name,
    count: overviewData[config.field] || 0,
    bgPic: config.bgPic,
  }));

  return (
    <div className="progress-component">
      {/* 指标卡片 */}
      <MetricsCard metrics={metricsData} />
      
      {/* 阶段总览 */}
      <StageOverview
        title="进度效率总览"
        todayData={stageData?.todayEveryStageStatistic || {}}
        compareData={stageData?.compareEveryStageStatistic || {}}
        isProgress={true}
        activeStages={activeStages}
        onStageClick={handleStageClick}
      />
      
      {/* 数据明细 */}
      <div className="data-detail">
        <div className="detail-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">数据明细</span>
        </div>
        
        <div ref={searchRef}>
          <CommonForm
            formConfig={searchFormConfig}
            defaultValue={{
              ...searchCondition,
              ...searchCondition.searchForm,
              fetchStationList: true,
            }}
            layout="inline"
            formType="search"
            getFormInstance={(ref) => (searchFormRef.current = ref)}
            onSearchClick={onSearchClick}
            onResetClick={onResetClick}
          />
        </div>
        
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey="progress-detail-table"
          columns={progressTableColumns}
          loading={loading}
          rowKey="stationBaseId"
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            setSearchCondition(value);
          }}
          showColumnSetting={true}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(ProgressComponent);
