import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { message, Select, Popconfirm } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import CustomTabs from '@/components/CustomTabs';
import showModal from '@/components/CommonModal';
import AcceptTaskModal from './components/AcceptTaskModal';
import CompleteTaskModal from './components/CompleteTaskModal';
import DownloadMapModal from './components/DownloadMapModal';
import {
  searchConfig,
  tableColumns,
  tabsConfig,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
  statusNameStyle,
  TaskStatus,
} from './utils/constant';
import { mapManageApi, commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  sortColumnsByState,
  createDefaultColumnsState,
} from '@jd/x-coreui/lib/components/CommonTable/columnUtils';
import { saveSearchValues } from '@/redux/reducer/searchForm';
import { formatDateToSecond } from '@/utils/utils';
import './index.scss';

const { Option } = Select;

const initSearchCondition = {
  // 表单组件所需特殊数据结构的字段
  provinceCityCountry: null,
  provinceAgencyArea: null,
  taskSubmitTime: null,
  // 列表接口入参字段
  searchForm: {
    stationNumber: null,
    vehicleModelType: null,
    mapVendor: null,
    durationValue: null,
    mileageValue: null,
    startTime: '',
    endTime: '',
    provinceId: null,
    cityId: null,
    countryId: null,
    companyCode: '',
    areaCode: '',
    durationOperator: 'gt',
    mileageOperator: 'gt',
    taskStatus: '',
  },
  // 页码字段
  pageNum: 1,
  pageSize: 10,
};

// 下拉框数据映射
const dropdownDataMap = {
  provinceCityCountry: {
    fetchApi: commonApi.getProvinceCityCountryList(),
    options: [],
  },
  provinceAgencyArea: {
    fetchApi: commonApi.getProvinceAgencyAreaList(),
    options: [],
  },
  stationNumber: {
    fetchApi: commonApi.getStationList(),
    options: [],
  },
  vehicleModelType: {
    fetchApi: commonApi.getVehicleModelList(),
    options: [],
  },
};

const MapManage = () => {
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchForm,
  );
  const navigator = useNavigate();
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);
  const acceptTaskRef = useRef<any>({});
  const completeTaskRef = useRef<any>({});

  const [searchCondition, setSearchCondition] = useState(() => {
    console.log('有历史数据吗', historySearchValues);
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [activeTabKey, setActiveTabKey] = useState<string>(() => {
    return historySearchValues.activeTabKey
      ? historySearchValues.activeTabKey
      : '';
  });
  const [countMap, setCountMap] = useState({
    TOTAL: 0,
    TO_REPORT: 0,
    TO_COLLECT: 0,
    COLLECTING: 0,
    TO_DRAW: 0,
    DRAWING: 0,
    VERIFYING: 0,
    COMPLETED: 0,
  });
  const [selectInfo, setSelectInfo] = useState<{
    selectedRowKeys: any[];
    selectedRows: any[];
    clearFunc: any;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    clearFunc: () => {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(searchConfig);
  const [durationOperator, setDurationOperator] = useState<string>(() => {
    return (
      historySearchValues.searchValues?.searchForm?.durationOperator || 'gt'
    );
  });
  const [mileageOperator, setMileageOperator] = useState<string>(() => {
    return (
      historySearchValues.searchValues?.searchForm?.mileageOperator || 'gt'
    );
  });

  const defaultColumnsState = useMemo(() => {
    return createDefaultColumnsState(
      tableColumns,
      defaultHiddenColumns,
      defaultLeftFixedColumns,
      defaultRightFixedColumns,
    );
  }, []);
  const [columnsState, setColumnsState] = useState<any>(defaultColumnsState);

  const { tableData, loading, reloadTable } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    mapManageApi.getMapManagePage,
  );

  useEffect(() => {
    initializeDropdownData();
  }, []);

  useEffect(() => {
    if ((tableData as any)?.countMap) {
      setCountMap((tableData as any).countMap);
    }
  }, [tableData]);

  const getRangeForm = useCallback((onChangeFunc: any) => {
    return (
      <Select
        style={{ width: 70 }}
        defaultValue="gt"
        onChange={(e) => {
          console.log('此时的e是多少', e);
          onChangeFunc(e);
        }}
      >
        <Option value="gt">大于</Option>
        <Option value="lt">小于</Option>
        <Option value="eq">等于</Option>
      </Select>
    );
  }, []);

  const initializeDropdownData = async () => {
    try {
      const allApi = Object.values(dropdownDataMap).map(
        (item) => item.fetchApi,
      );
      const allRes = await Promise.all(allApi);
      const dropdownKeys = Object.keys(dropdownDataMap);
      allRes?.forEach((res, index) => {
        if (res && res.code === HttpStatusCode.Success && res.data) {
          const currentKey = dropdownKeys[index];
          dropdownDataMap[currentKey].options = res.data;
        }
      });
      const newSearchConfig = {
        ...searchConfig,
        fields: searchConfig.fields.map((field) => {
          switch (field.fieldName) {
            case 'provinceCityCountry':
              return {
                ...field,
                options: dropdownDataMap.provinceCityCountry.options,
              };
            case 'provinceAgencyArea':
              return {
                ...field,
                options: dropdownDataMap.provinceAgencyArea.options,
              };
            case 'stationNumber':
              return {
                ...field,
                options: dropdownDataMap.stationNumber.options,
              };
            case 'vehicleModelType':
              return {
                ...field,
                options: dropdownDataMap.vehicleModelType.options,
              };
            case 'durationValue':
              const durationForm = getRangeForm(setDurationOperator);
              return {
                ...field,
                addonBefore: durationForm,
              };
            case 'mileageValue':
              const mileageForm = getRangeForm(setMileageOperator);
              return {
                ...field,
                addonBefore: mileageForm,
              };
            default:
              return field;
          }
        }),
      };
      setSearchFormConfig(newSearchConfig);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
      message.error('初始化下拉框数据失败');
    }
  };

  const onSearchClick = (values: any) => {
    const newCondition = formatSearchValues(values, { pageNum: 1 });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition);
  };

  const onResetClick = () => {
    const resetValues = {
      ...initSearchCondition,
      searchForm: {
        ...initSearchCondition.searchForm,
        taskStatus: activeTabKey || '',
      },
    };
    setSearchCondition(resetValues);
    saveHistorySearchValue(resetValues);
    if (searchFormRef.current) {
      searchFormRef.current.resetFields();
    }
  };

  const formatSearchValues = (values: any, currentData: any) => {
    const {
      taskSubmitTime,
      provinceCityCountry,
      provinceAgencyArea,
      ...otherValues
    } = values;
    const { taskStatus, pageNum, pageSize } = currentData;
    const formatTime: any =
      taskSubmitTime?.length > 0 ? formatDateToSecond(taskSubmitTime) : {};
    const newSearchCondition = {
      taskSubmitTime,
      provinceCityCountry,
      provinceAgencyArea,
      searchForm: {
        ...otherValues,
        startTime: formatTime.startTime
          ? `${formatTime.startTime.split(' ')[0]} 00:00:00`
          : '',
        endTime: formatTime.endTime
          ? `${formatTime.endTime.split(' ')[0]} 23:59:59`
          : '',
        provinceId: provinceCityCountry?.[0] || null,
        cityId: provinceCityCountry?.[1] || null,
        countryId: provinceCityCountry?.[2] || null,
        companyCode: provinceAgencyArea?.[0] || '',
        areaCode: provinceAgencyArea?.[1] || '',
        durationOperator: durationOperator,
        mileageOperator: mileageOperator,
        taskStatus:
          taskStatus === undefined || taskStatus === null
            ? activeTabKey
            : taskStatus,
      },
      pageNum:
        pageNum === undefined || pageNum === null
          ? searchCondition.pageNum
          : pageNum,
      pageSize:
        pageSize === undefined || pageSize === null
          ? searchCondition.pageSize
          : pageSize,
    };
    return newSearchCondition;
  };

  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    const currentSearchForm = searchFormRef.current?.getFieldsValue() || {};
    const newCondition = formatSearchValues(currentSearchForm, {
      pageNum: 1,
      taskStatus: key,
    });
    setSearchCondition(newCondition);
    saveHistorySearchValue(newCondition, key);
    selectInfo.clearFunc();
    setSelectInfo({
      selectedRowKeys: [],
      selectedRows: [],
      clearFunc: () => {},
    });
  };

  const handleCheckRouteDetail = (record: any) => {
    if (!record.id || !record.taskName) {
      message.error('地图线路信息异常');
      return;
    }
    const params = {
      taskId: record.id?.toString() || '',
      taskName: record.taskName?.toString() || '',
    };
    navigator('/app/deployMap', {
      state: { autoSearchTaskInfo: params },
    });
  };

  const renderOperationButtons = (record: any) => {
    const baseBtnConfig = [
      {
        key: 'view',
        text: '查看',
        onClick: () => {
          handleCheckRouteDetail(record);
        },
      },
    ];
    let btnConfig = [...baseBtnConfig];
    switch (record.taskStatus) {
      case TaskStatus.TO_REPORT:
        btnConfig.unshift({
          key: 'submit',
          text: '提交',
          onClick: () => {},
        });
        break;
      case TaskStatus.TO_COLLECT:
      case TaskStatus.TO_DRAW:
        btnConfig.unshift({
          key: 'takeTask',
          text: '领取任务',
          onClick: () => {
            handleAcceptTask(record);
          },
        });
        break;
      case TaskStatus.COLLECTING:
      case TaskStatus.DRAWING:
      case TaskStatus.VERIFYING:
        btnConfig.unshift({
          key: 'complete',
          text: '完成',
          onClick: () => {
            handleCompleteTask(record);
          },
        });
        break;
      default:
        break;
    }
    return (
      <>
        {btnConfig.map((btn, index) => {
          if (btn.key === 'submit') {
            // 提交按钮
            return (
              <Popconfirm
                key={btn.key}
                title="确认提交该任务吗？"
                onConfirm={() => handleSubmitTask(false, Number(record.id))}
                okText="确定"
                cancelText="取消"
                placement="left"
                overlayStyle={{ maxWidth: 800 }}
              >
                <a
                  style={{
                    marginRight: index < btnConfig.length - 1 ? 8 : 0,
                  }}
                >
                  {btn.text}
                </a>
              </Popconfirm>
            );
          }
          // 其他按钮
          return (
            <a
              key={btn.key}
              onClick={btn.onClick}
              style={{
                marginRight: index < btnConfig.length - 1 ? 8 : 0,
              }}
            >
              {btn.text}
            </a>
          );
        })}
      </>
    );
  };

  const formatColumns = useMemo(() => {
    return tableColumns.map((col) => {
      switch (col?.dataIndex) {
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => {
              return (
                <div className="operate">{renderOperationButtons(record)}</div>
              );
            },
          };
        case 'taskStatusName':
          return {
            ...col,
            render: (text: any, record: any) => {
              const textColor =
                statusNameStyle[record?.taskStatus]?.textColor ||
                'rgba(0, 0, 0, 0.88)';
              const bgColor =
                statusNameStyle[record?.taskStatus]?.bgColor || '#fff';
              return text ? (
                <div
                  style={{
                    color: textColor,
                    backgroundColor: bgColor,
                    borderRadius: '5px',
                    width: 'fit-content',
                    padding: '3px',
                  }}
                >
                  {text}
                </div>
              ) : (
                '-'
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text ?? '-'}`,
          };
      }
    });
  }, []);

  const dynamicColumns = useMemo(() => {
    return sortColumnsByState(formatColumns, columnsState) || [];
  }, [formatColumns, columnsState]);

  const saveHistorySearchValue = (
    searchValues: any,
    currentTabKey?: string,
  ) => {
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues,
        activeTabKey:
          currentTabKey === undefined || currentTabKey === null
            ? activeTabKey
            : currentTabKey,
      }),
    );
  };

  // 提交任务
  const handleSubmitTask = async (isBatch: boolean, taskId?: number) => {
    let taskIdList: number[] = [];
    if (isBatch) {
      taskIdList = selectInfo.selectedRowKeys.map((id: any) => parseInt(id));
    } else if (taskId) {
      taskIdList = [taskId];
    }
    if (taskIdList.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    try {
      const res = await mapManageApi.submitTaskList({
        taskIdList,
      });
      if (res && res.code === HttpStatusCode.Success) {
        message.success('提交成功');
        if (isBatch) {
          selectInfo.clearFunc();
          setSelectInfo({
            selectedRowKeys: [],
            selectedRows: [],
            clearFunc: () => {},
          });
        }
        reloadTable();
      } else {
        message.error(res.message || '批量提交失败');
      }
    } catch (error) {
      console.error('批量提交失败:', error);
      message.error('批量提交失败');
    }
  };

  // 领取任务
  const handleAcceptTask = (record: any) => {
    console.log('领取任务操作', record);
    const title =
      record.taskStatus === TaskStatus.TO_COLLECT
        ? '采图任务领取'
        : '制图任务领取';
    showModal({
      title,
      width: 600,
      content: (
        <AcceptTaskModal
          taskId={record.id}
          ref={(ref) => (acceptTaskRef.current = ref)}
        />
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okText: '开始任务',
        cancelText: '取消',
        okFunc: async (closeModal) => {
          try {
            if (acceptTaskRef.current?.handleSubmit) {
              await acceptTaskRef.current.handleSubmit();
              reloadTable();
              closeModal();
            }
          } catch (error) {
            console.error('领取任务验证失败:', error);
          }
        },
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  // 完成任务
  const handleCompleteTask = (record: any) => {
    const title =
      record.taskStatus === TaskStatus.COLLECTING
        ? '采图任务完成确认'
        : record.taskStatus === TaskStatus.DRAWING
        ? '制图任务完成确认'
        : '验图任务完成确认';
    showModal({
      title,
      width: 600,
      content: (
        <CompleteTaskModal
          taskId={record.id}
          ref={(ref) => (completeTaskRef.current = ref)}
        />
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okText: '完成任务',
        cancelText: '取消',
        okFunc: async (closeModal) => {
          try {
            if (completeTaskRef.current?.handleSubmit) {
              await completeTaskRef.current.handleSubmit();
              reloadTable();
              closeModal();
            }
          } catch (error) {
            console.error('完成任务验证失败:', error);
          }
        },
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  // 批量提交
  const showBatchSubmitModal = () => {
    const selectedCount = selectInfo.selectedRowKeys?.length || 0;
    if (selectedCount === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    showModal({
      title: '批量提交确认',
      width: 500,
      content: (
        <div style={{ padding: '20px 0' }}>
          <p>确认要提交选中的 {selectedCount} 个任务吗？</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            提交后任务状态将发生变更，请确认操作。
          </p>
        </div>
      ),
      footer: {
        showOk: true,
        showCancel: true,
        okText: '确定提交',
        cancelText: '取消',
        okFunc: async (closeModal) => {
          try {
            await handleSubmitTask(true);
            closeModal();
          } catch (error) {
            console.error('批量提交失败:', error);
          }
        },
        cancelFunc: (closeModal) => {
          closeModal();
        },
      },
    });
  };

  // 下载地图
  const handleDownloadMap = () => {
    if (selectInfo.selectedRowKeys?.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    const selectedTaskIds = selectInfo.selectedRowKeys
      .map((id: any) => Number(id))
      .filter((id: any) => !isNaN(id));
    showModal({
      title: '请选择下载的线路类型',
      width: 600,
      content: <DownloadMapModal selectedTaskIds={selectedTaskIds} />,
      footer: {
        showOk: false,
        showCancel: true,
        cancelText: '关闭',
        cancelFunc: (closeModal) => {
          selectInfo.clearFunc();
          setSelectInfo({
            selectedRowKeys: [],
            selectedRows: [],
            clearFunc: () => {},
          });
          closeModal();
        },
      },
    });
  };

  const middleBtns: any[] = [
    {
      show: true,
      title: '批量提交',
      key: 'batchSubmit',
      onClick: () => showBatchSubmitModal(),
    },
    {
      show: true,
      title: '下载地图',
      key: 'downloadMap',
      onClick: handleDownloadMap,
    },
  ];

  const formatTabsConfig = useMemo(() => {
    return tabsConfig.map((tab) => ({
      key: tab.key,
      label: tab.label,
      count: countMap?.[tab.statusKey as keyof typeof countMap] || 0,
    }));
  }, [countMap]);

  return (
    <div className="map-manage">
      <div ref={searchRef}>
        <CommonForm
          formConfig={searchFormConfig}
          defaultValue={{
            ...searchCondition,
            ...searchCondition.searchForm,
          }}
          layout="inline"
          formType="search"
          getFormInstance={(ref) => (searchFormRef.current = ref)}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
        />
      </div>
      <div className="table-container">
        <CustomTabs
          items={formatTabsConfig}
          activeKey={activeTabKey}
          onChange={handleTabChange}
        />
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey={'map-manage-table'}
          columns={dynamicColumns}
          loading={loading}
          rowKey="id"
          middleBtns={middleBtns}
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            console.log('此时的分页变化', value);
            saveHistorySearchValue(value);
            setSearchCondition(value);
          }}
          crossPageSelect={(keys: any, rows: any, clearFunc: any) => {
            setSelectInfo({
              selectedRowKeys: keys,
              selectedRows: rows,
              clearFunc: clearFunc,
            });
          }}
          // 列配置相关属性
          showColumnSetting={true}
          columnsState={{
            value: columnsState,
            onChange: setColumnsState,
            persistenceType: 'localStorage',
          }}
          defaultColumnsState={defaultColumnsState}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(MapManage);
