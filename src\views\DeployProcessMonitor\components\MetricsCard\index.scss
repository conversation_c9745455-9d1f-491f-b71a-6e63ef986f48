.metrics-card {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;

  .metric-item {
    height: 120px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .metric-content {
      position: absolute;
      left: 24px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;

      .metric-count {
        font-size: 32px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 8px;
        display: flex;
        align-items: baseline;

        .metric-unit {
          font-size: 16px;
          font-weight: normal;
          margin-left: 4px;
        }
      }

      .metric-name {
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.2;
      }
    }
  }
}
