import React from 'react';
import { FormConfig } from '@/components/CommonForm/formConfig';

// 第一个卡片的表单配置
export const filterFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'provinceCityCountry',
      label: '省市区',
      placeholder: '请选择省市区',
      type: 'cascader',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'name', value: 'id' },
      xxl: 8,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'provinceAgencyArea',
      label: '省区片区',
      placeholder: '请选择省区片区',
      type: 'cascader',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'name', value: 'code' },
      xxl: 8,
      xl: 8,
      lg: 12,
    },
  ],
};

// 阶段数据配置
export const stageConfig = [
  {
    category: '需求部署',
    stages: [
      {
        key: 'reject',
        name: '需求',
        field: 'requirementCount',
        durationField: 'requirementDuration',
      },
      {
        key: 'ordering',
        name: '下单',
        field: 'orderCount',
        durationField: 'orderDuration',
      },
      {
        key: 'supplying',
        name: '配车',
        field: 'supplyCount',
        durationField: 'supplyDuration',
      },
    ],
  },
  {
    category: '车辆部署',
    stages: [
      {
        key: 'insuring',
        name: '保险',
        field: 'insuranceCount',
        durationField: 'insuranceDuration',
      },
      {
        key: 'dispatching',
        name: '发运',
        field: 'dispatchCount',
        durationField: 'dispatchDuration',
      },
      {
        key: 'receiving',
        name: '到车',
        field: 'receiveCount',
        durationField: 'receiveDuration',
      },
    ],
  },
  {
    category: '地图部署',
    stages: [
      {
        key: 'drawing',
        name: '画图',
        field: 'mapDrawCount',
        durationField: 'mapDrawDuration',
      },
      {
        key: 'collecting',
        name: '采图',
        field: 'mapCollectionCount',
        durationField: 'mapCollectionDuration',
      },
      {
        key: 'making',
        name: '制图',
        field: 'mapMakeCount',
        durationField: 'mapMakeDuration',
      },
      {
        key: 'map_checking',
        name: '验图',
        field: 'mapCheckCount',
        durationField: 'mapCheckDuration',
      },
    ],
  },
  {
    category: '交付部署',
    stages: [
      {
        key: 'checking',
        name: '验收',
        field: 'checkOrderCount',
        durationField: 'checkOrderDuration',
      },
      {
        key: 'delivering',
        name: '交付',
        field: 'deliveryCount',
        durationField: 'deliveryDuration',
      },
    ],
  },
];

// 进度指标配置
export const progressMetricsConfig = [
  {
    name: '部署站点总数',
    field: 'deployCompleteStationCount',
    bgPic: require('@/assets/image/deployProcess/schedule-delivered-sites.png'),
  },
  {
    name: '部署车辆总数',
    field: 'deployCompleteVehicleCount',
    bgPic: require('@/assets/image/deployProcess/schedule-delivered-vehicles.png'),
  },
  {
    name: '部署中站点',
    field: 'deployingStationCount',
    bgPic: require('@/assets/image/deployProcess/schedule-undelivered-sites.png'),
  },
  {
    name: '部署中车辆',
    field: 'deployingVehicleCount',
    bgPic: require('@/assets/image/deployProcess/schedule-undelivered-vehicles.png'),
  },
];

// 效率指标配置
export const efficiencyMetricsConfig = [
  {
    name: '单站平均总部署时长',
    field: 'stationAvgDeploymentDuration',
    bgPic: require('@/assets/image/deployProcess/efficiency-site-deployment.png'),
    unit: '天',
  },
  {
    name: '补车平均部署时长',
    field: 'vehicleAvgDeploymentDuration',
    bgPic: require('@/assets/image/deployProcess/efficiency-vehicle-deployment.png'),
    unit: '天',
  },
  {
    name: '补图平均部署时长',
    field: 'mapAvgDeploymentDuration',
    bgPic: require('@/assets/image/deployProcess/efficiency-map-deployment.png'),
    unit: '天',
  },
];

// 进度组件的搜索表单配置
export const progressSearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'expectedDeliveryTime',
      label: '预期交付月份',
      type: 'datePicker',
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'deliveryCompleteTime',
      label: '实际交付时间',
      type: 'rangeTime',
      xxl: 8,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'stationName', value: 'stationNumber' },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'orderNumber',
      label: '订单编号',
      placeholder: '请输入订单编号',
      type: 'input',
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 效率组件的搜索表单配置
export const efficiencySearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'expectedDeliveryTime',
      label: '预期交付月份',
      type: 'datePicker',
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'deliveryDuration',
      label: '交付天数',
      type: 'inputNumber',
      xxl: 8,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'stationNumber',
      label: '站点名称',
      placeholder: '请选择站点',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      mapRelation: { label: 'stationName', value: 'stationNumber' },
      xxl: 6,
      xl: 8,
      lg: 12,
    },
    {
      fieldName: 'orderNumber',
      label: '订单编号',
      placeholder: '请输入订单编号',
      type: 'input',
      xxl: 6,
      xl: 8,
      lg: 12,
    },
  ],
};

// 进度表格列配置
export const progressTableColumns = [
  {
    title: '基本信息',
    children: [
      {
        title: '省市区',
        dataIndex: 'location',
        key: 'location',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '省区片区',
        dataIndex: 'area',
        key: 'area',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '站点名称',
        dataIndex: 'stationName',
        key: 'stationName',
        width: 150,
        fixed: 'left',
      },
      {
        title: '站点编号',
        dataIndex: 'stationNumber',
        key: 'stationNumber',
        width: 150,
        fixed: 'left',
        hidden: true,
      },
    ],
  },
  {
    title: '订单信息',
    children: [
      {
        title: '订单编号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: 150,
      },
      {
        title: '车辆数',
        dataIndex: 'orderVehicleCount',
        key: 'orderVehicleCount',
        width: 100,
      },
      {
        title: '需求提报时间',
        dataIndex: 'requirementCreateTime',
        key: 'requirementCreateTime',
        width: 180,
      },
    ],
  },
  {
    title: '部署状态',
    children: [
      {
        title: '需求状态',
        dataIndex: 'requirementStatus',
        key: 'requirementStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '订单状态',
        dataIndex: 'orderStatus',
        key: 'orderStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '配车状态',
        dataIndex: 'supplyStatus',
        key: 'supplyStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '保险状态',
        dataIndex: 'insuranceStatus',
        key: 'insuranceStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '发车状态',
        dataIndex: 'dispatchStatus',
        key: 'dispatchStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '到车状态',
        dataIndex: 'receiveStatus',
        key: 'receiveStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '画图状态',
        dataIndex: 'mapDrawStatus',
        key: 'mapDrawStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '采图状态',
        dataIndex: 'mapCollectionStatus',
        key: 'mapCollectionStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '制图状态',
        dataIndex: 'mapMakeStatus',
        key: 'mapMakeStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '验图状态',
        dataIndex: 'mapCheckStatus',
        key: 'mapCheckStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '验收状态',
        dataIndex: 'checkOrderStatus',
        key: 'checkOrderStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
      {
        title: '交付状态',
        dataIndex: 'deliveryStatus',
        key: 'deliveryStatus',
        width: 100,
        render: (value: number) => (value === 1 ? '已完成' : '进行中'),
      },
    ],
  },
  {
    title: '时间信息',
    children: [
      {
        title: '实际交付时间',
        dataIndex: 'deliveryCompleteTime',
        key: 'deliveryCompleteTime',
        width: 180,
        fixed: 'right',
      },
      {
        title: '预期交付月份',
        dataIndex: 'expectedDeliveryTime',
        key: 'expectedDeliveryTime',
        width: 150,
        fixed: 'right',
      },
    ],
  },
];

// 效率表格列配置
export const efficiencyTableColumns = [
  {
    title: '基本信息',
    children: [
      {
        title: '省市区',
        dataIndex: 'location',
        key: 'location',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '省区片区',
        dataIndex: 'area',
        key: 'area',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '站点名称',
        dataIndex: 'stationName',
        key: 'stationName',
        width: 150,
        fixed: 'left',
      },
      {
        title: '站点编号',
        dataIndex: 'stationNumber',
        key: 'stationNumber',
        width: 150,
        fixed: 'left',
        hidden: true,
      },
    ],
  },
  {
    title: '订单信息',
    children: [
      {
        title: '订单编号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: 150,
      },
      {
        title: '车辆数',
        dataIndex: 'orderVehicleCount',
        key: 'orderVehicleCount',
        width: 100,
      },
      {
        title: '需求提报时间',
        dataIndex: 'requirementCreateTime',
        key: 'requirementCreateTime',
        width: 180,
      },
    ],
  },
  {
    title: '部署时长（天）',
    children: [
      {
        title: '需求天数',
        dataIndex: 'requirementDuration',
        key: 'requirementDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{
              color: record.requirementOverLimit ? '#ff4d4f' : '#52c41a',
            }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '订单天数',
        dataIndex: 'orderDuration',
        key: 'orderDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.orderOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '配车天数',
        dataIndex: 'supplyDuration',
        key: 'supplyDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.supplyOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '保险天数',
        dataIndex: 'insuranceDuration',
        key: 'insuranceDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.insuranceOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '发车天数',
        dataIndex: 'dispatchDuration',
        key: 'dispatchDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.dispatchOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '到车天数',
        dataIndex: 'receiveDuration',
        key: 'receiveDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.receiveOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '画图天数',
        dataIndex: 'mapDrawDuration',
        key: 'mapDrawDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.mapDrawOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '采图天数',
        dataIndex: 'mapCollectionDuration',
        key: 'mapCollectionDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{
              color: record.mapCollectionOverLimit ? '#ff4d4f' : '#52c41a',
            }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '制图天数',
        dataIndex: 'mapMakeDuration',
        key: 'mapMakeDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.mapMakeOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '验图天数',
        dataIndex: 'mapCheckDuration',
        key: 'mapCheckDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.mapCheckOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '验收天数',
        dataIndex: 'checkOrderDuration',
        key: 'checkOrderDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{
              color: record.checkOrderOverLimit ? '#ff4d4f' : '#52c41a',
            }}
          >
            {value}
          </span>
        ),
      },
      {
        title: '交付天数',
        dataIndex: 'deliveryDuration',
        key: 'deliveryDuration',
        width: 100,
        render: (value: number, record: any) => (
          <span
            style={{ color: record.deliveryOverLimit ? '#ff4d4f' : '#52c41a' }}
          >
            {value}
          </span>
        ),
      },
    ],
  },
  {
    title: '时间信息',
    children: [
      {
        title: '交付天数',
        dataIndex: 'totalDuration',
        key: 'totalDuration',
        width: 120,
        fixed: 'right',
      },
      {
        title: '预期交付月份',
        dataIndex: 'expectedDeliveryTime',
        key: 'expectedDeliveryTime',
        width: 150,
        fixed: 'right',
      },
    ],
  },
];

// 默认隐藏的列
export const defaultHiddenColumns = ['location', 'area', 'stationNumber'];

// 默认左侧固定的列
export const defaultLeftFixedColumns = ['stationName'];

// 默认右侧固定的列
export const defaultRightFixedColumns = [
  'totalDuration',
  'expectedDeliveryTime',
  'deliveryCompleteTime',
];
