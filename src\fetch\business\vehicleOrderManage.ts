import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

// Mock数据
const vehicleOrderPageMockData = {
  code: '0000',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 10,
    total: 95,
    countMap: {
      TOTAL: 95,
      CREATED: 25,
      WAIT_SHIPMENT: 20,
      SHIPPED: 18,
      ARRIVED: 17,
      COMPLETED: 15,
    },
    list: Array.from({ length: 10 }, (_, index) => ({
      id: `${index + 1}`,
      orderNumber: `ORD01${(index + 1).toString().padStart(3, '0')}`,
      provinceId: 1,
      cityId: 11,
      countryId: 111,
      countryName: '北京市通州区',
      companyCode: 'BJ001',
      areaCode: 'CY001',
      areaName: '华北区-朝阳片区',
      stationName: `站点${index + 1}`,
      stationNumber: `ST${(index + 1).toString().padStart(4, '0')}`,
      vehicleModelType: 'JD_6EF',
      vehicleModelName: 'JD-6EF型配送车',
      vehicleNameList: 'JDE6021,JDE6022',
      insuranceNoList: 'INS001,INS002',
      count: Math.floor(Math.random() * 10) + 1,
      allocationCount: Math.floor(Math.random() * 5),
      dispatchCount: Math.floor(Math.random() * 3),
      contact: `联系人${index + 1}`,
      contactPhone: `138${(index + 1).toString().padStart(8, '0')}`,
      address: `详细地址${index + 1}`,
      status: ['CREATED', 'WAIT_SHIPMENT', 'SHIPPED', 'ARRIVED', 'COMPLETED'][
        index % 5
      ],
      statusName: ['已下单', '待发运', '已发运', '已到车', '已完成'][index % 5],
      expectedDeliveryMonth: '2025-08',
      createTime: '2025-08-10 10:30:00',
      modifyTime: '2025-08-10 14:30:00',
    })),
  },
};

// 订单详情Mock数据
const orderDetailMockData = {
  code: '0000',
  data: {
    orderNumber: 'ORD202508110001',
    count: 5,
    version: 1, // 添加版本号字段
    contryName: '北京-北京市-朝阳区',
    areaName: '北京省区-北京北区',
    stationName: '北京朝阳站',
    stationNumber: 'BJ_CY_001',
    vehicleModelTypeName: 'A型无人配送车',
    vehicleNameList: [
      {
        vehicleName: 'JD001',
        vehicleTypeId: 19,
        vehicleTypeName: '智梭-0.8T-V3.0-一径雷达4-森云环视4-森云红绿灯2-00',
        serialNo: 'SN20250811001',
        vehicleNumber: '京A12345',
      },
      {
        vehicleName: 'JD002',
        vehicleTypeId: 78,
        vehicleTypeName: '金龙-0.8T-V2.0-大疆雷达4-灰点环视2-灰点红绿灯2-00',
        serialNo: 'SN20250811002',
        vehicleNumber: '京A12346',
      },
    ],
    insuranceNo: '保险单号：INS202508110001，保险单号：INS202508110002',
    supplier: '京东物流科技有限公司',
    supplierUser: '张三',
    supplierContact: '13800138001',
    address: '北京市朝阳区建国路88号SOHO现代城',
    contact: '李四',
    contactPhone: '13800138002',
    mail: '<EMAIL>',
    alternateContact: '王五',
    alternateContactPhone: '13800138003',
    driverName: '赵六',
    contactInfo: '13800138004',
    idNumber: '110101199001011234',
    insurance: 50000,
    vehiclePlateNumber: '京A12345',
    vehicleModel: 'A型无人配送车-标准版',
    expectedDeliveryMonth: '2025-08',
    estimatedArrivalTime: '2025-08-19',
    loadAddress: '北京市朝阳区京东总部大厦',
  },
};

// 发运接口Mock数据
const dispatchVehicleMockData = {
  code: '0000',
  message: '发运成功',
};

// 配车接口Mock数据
const assignVehicleMockData = {
  code: '0000',
  message: '配车成功',
};

// 验收接口Mock数据
const checkVehicleMockData = {
  code: '0000',
  message: '验收成功',
};

// 订单编辑接口Mock数据
const editOrderMockData = {
  code: '0000',
  message: '编辑成功',
};

// 修改记录接口Mock数据
const pageUpdateLogMockData = {
  code: '0000',
  data: {
    list: [
      {
        id: 1,
        userName: 'admin',
        modifyTime: '2025-01-11 10:30:00',
        fieldName: '承运人',
        beforeValue: '张三',
        afterValue: '李四',
      },
      {
        id: 2,
        userName: 'admin',
        modifyTime: '2025-01-11 09:15:00',
        fieldName: '联系方式',
        beforeValue: '13800138001',
        afterValue: '13800138002',
      },
      {
        id: 3,
        userName: 'operator',
        modifyTime: '2025-01-10 16:45:00',
        fieldName: '预计到车时间',
        beforeValue: '2025-01-15',
        afterValue: '2025-01-20',
      },
      {
        id: 4,
        userName: 'operator',
        modifyTime: '2025-01-10 14:20:00',
        fieldName: '备选联系人',
        beforeValue: '',
        afterValue: '王五',
      },
    ],
    total: 4,
    pages: 1,
  },
};

// 到车接口Mock数据
const arrivedVehicleMockData = {
  code: '0000',
  message: '到车成功',
};

// 接口参数类型定义
export interface VehicleOrderPageRequest {
  startTime?: string;
  endTime?: string;
  provinceId?: number | null;
  cityId?: number | null;
  countryId?: number | null;
  companyCode?: string;
  areaCode?: string;
  stationNumber?: string;
  vehicleModelType?: string;
  orderNumber?: string;
  status?: string;
  expectedDeliveryMonth?: string;
  pageNum: number;
  pageSize: number;
}

// 发运接口请求参数
export interface DispatchVehicleRequest {
  id: number;
  driverName: string;
  idNumber?: string;
  contact: string;
  insuranceMoney?: number;
  vehiclePlateNumber?: string;
  vehicleModel?: string;
  estimatedArrivalTime: string;
  loadAddress?: string;
}

// 配车接口请求参数
export interface AssignVehicleRequest {
  id: number;
  vehicleList: {
    vehicleName: string;
    serialNo: string;
    vehicleTypeId: number;
    vehicleTypeName: string;
    vehicleNumber?: string;
  }[];
}

// 验收接口请求参数
export interface CheckVehicleRequest {
  id: number;
  checkUser: string;
  vehicleAppearanceStatus: boolean;
  vehiclePowerStatus: boolean;
  chargerEquippedStatus: boolean;
  spareKeyEquippedStatus: boolean;
  imageList?: {
    type?: string;
    fileKey?: string;
    bucketName?: string;
  }[];
  checkRemark?: string;
}

// 订单编辑接口请求参数
export interface EditOrderRequest {
  ordertId: number;
  version: number;
  currentData: {
    userName: string;
    contact: string;
    idNumber?: string;
    insurance?: number;
    vehiclePlateNumber?: string;
    vehicleModel?: string;
    estimatedArrivalTime: string;
    loadAddress?: string;
  };
}

// 修改记录接口请求参数
export interface PageUpdateLogRequest {
  orderId: number;
  pageNum: number;
  pageSize: number;
}

// 到车接口请求参数
export interface ArrivedVehicleRequest {
  id: number;
}

export interface VehicleOrderItem {
  id: string;
  orderNumber: string;
  provinceId: number;
  cityId: number;
  countryId: number;
  provinceName: string;
  cityName: string;
  countryName: string;
  companyCode: string;
  areaCode: string;
  areaName: string;
  stationName: string;
  stationNumber: string;
  vehicleModelType: string;
  vehicleModelName: string;
  vehicleNameList?: string;
  insuranceNoList?: string;
  count: number;
  allocationCount: number;
  dispatchCount: number;
  contact: string;
  contactPhone: string;
  address: string;
  status: string;
  statusName: string;
  expectedDeliveryMonth?: string;
  createTime: string;
  modifyTime: string;
}

export interface VehicleOrderPageResponse {
  pageNum: number;
  pageSize: number;
  pages: number;
  total: number;
  countMap: {
    TOTAL: number;
    CREATED: number;
    WAIT_SHIPMENT: number;
    SHIPPED: number;
    ARRIVED: number;
    COMPLETED: number;
  };
  list: VehicleOrderItem[];
}

class VehicleOrderManageApi {
  // 分页查询订单
  public async getVehicleOrderPage(
    params: VehicleOrderPageRequest,
  ): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/page',
      method: Method.POST,
      body: params,
    };
    console.log('调用分页查询订单接口了！！', params);

    return Promise.resolve(vehicleOrderPageMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 查看订单详情
  public async getDetailById(id: number): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/getDetailById',
      method: Method.POST,
      body: { id },
    };
    console.log('调用查看订单详情接口了！！', id);

    return Promise.resolve(orderDetailMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 发运
  public async dispatchVehicle(params: DispatchVehicleRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/dispatchVehicle',
      method: Method.POST,
      body: params,
    };
    console.log('调用发运接口了！！', params);

    return Promise.resolve(dispatchVehicleMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 配车
  public async assignVehicle(params: AssignVehicleRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/assignVehicle',
      method: Method.POST,
      body: params,
    };
    console.log('调用配车接口了！！', params);

    return Promise.resolve(assignVehicleMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 验收
  public async checkVehicle(params: CheckVehicleRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/checkVehicle',
      method: Method.POST,
      body: params,
    };
    console.log('调用验收接口了！！', params);

    return Promise.resolve(checkVehicleMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 订单编辑
  public async editOrder(params: EditOrderRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/edit',
      method: Method.POST,
      body: params,
    };
    console.log('调用订单编辑接口了！！', params);

    return Promise.resolve(editOrderMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 修改记录
  public async pageUpdateLog(params: PageUpdateLogRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/pageUpdateLog',
      method: Method.POST,
      body: params,
    };
    console.log('调用修改记录接口了！！', params);

    return Promise.resolve(pageUpdateLogMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 到车
  public async arrivedVehicle(params: ArrivedVehicleRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/order/arrivedVehicle',
      method: Method.POST,
      body: params,
    };
    console.log('调用到车接口了！！', params);

    return Promise.resolve(arrivedVehicleMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }
}

export const vehicleOrderManageApi = new VehicleOrderManageApi();
