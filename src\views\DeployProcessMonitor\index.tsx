import React, { useState, useEffect, useRef } from 'react';
import { Segmented, message } from 'antd';
import { CommonForm } from '@jd/x-coreui';
import ProgressComponent from './components/ProgressComponent';
import EfficiencyComponent from './components/EfficiencyComponent';
import { deployProcessMonitorApi } from '@/fetch/business/deployProcessMonitor';
import { commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import { filterFormConfig } from './utils/constant';
import './index.scss';

const DeployProcessMonitor: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'progress' | 'efficiency'>(
    'progress',
  );
  const [dimension, setDimension] = useState<'day' | 'week' | 'month'>('day');
  const [filterCondition, setFilterCondition] = useState<any>({});
  const [stageData, setStageData] = useState<any>({});
  const [formConfig, setFormConfig] = useState(filterFormConfig);
  const filterFormRef = useRef<any>(null);

  // 初始化下拉框数据
  useEffect(() => {
    initializeDropdownData();
    loadStageData();
  }, []);

  // 监听维度变化
  useEffect(() => {
    if (dimension) {
      loadStageData();
    }
  }, [dimension, filterCondition]);

  const initializeDropdownData = async () => {
    try {
      const [provinceCityRes, provinceAgencyRes] = await Promise.all([
        commonApi.getProvinceCityCountryList(),
        commonApi.getProvinceAgencyAreaList(),
      ]);

      const newFormConfig = {
        ...formConfig,
        fields: formConfig.fields.map((field: any) => {
          if (field.fieldName === 'provinceCityCountry') {
            return {
              ...field,
              options:
                provinceCityRes.code === HttpStatusCode.Success
                  ? provinceCityRes.data
                  : [],
            };
          }
          if (field.fieldName === 'provinceAgencyArea') {
            return {
              ...field,
              options:
                provinceAgencyRes.code === HttpStatusCode.Success
                  ? provinceAgencyRes.data
                  : [],
            };
          }
          return field;
        }),
      };
      setFormConfig(newFormConfig);
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
    }
  };

  const loadStageData = async () => {
    try {
      const apiMethod =
        activeTab === 'progress'
          ? deployProcessMonitorApi.getProgressEveryStage
          : deployProcessMonitorApi.getEfficiencyEveryStage;

      const res = await apiMethod({
        ...filterCondition,
        dimension,
      });

      if (res.code === HttpStatusCode.Success) {
        setStageData(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('加载阶段数据失败:', error);
      message.error('加载数据失败');
    }
  };

  const handleFilterChange = (values: any, changedFieldName: string) => {
    // 实现互斥逻辑：当一个搜索项有值时，另外的筛选项需要变为disable置灰不可编辑
    const hasProvinceCityCountry =
      values.provinceCityCountry && values.provinceCityCountry.length > 0;
    const hasProvinceAgencyArea =
      values.provinceAgencyArea && values.provinceAgencyArea.length > 0;

    const newFormConfig = {
      ...formConfig,
      fields: formConfig.fields.map((field: any) => {
        if (field.fieldName === 'provinceCityCountry') {
          return {
            ...field,
            disabled: hasProvinceAgencyArea,
          };
        }
        if (field.fieldName === 'provinceAgencyArea') {
          return {
            ...field,
            disabled: hasProvinceCityCountry,
          };
        }
        return field;
      }),
    };
    setFormConfig(newFormConfig);

    // 构造筛选条件
    const condition: any = {};

    if (hasProvinceCityCountry) {
      const [provinceId, cityId, countyId] = values.provinceCityCountry;
      condition.provinceId = provinceId;
      condition.cityId = cityId;
      condition.countyId = countyId;
    }

    if (hasProvinceAgencyArea) {
      const [qlProvinceAgencyCode, qlAreaCode] = values.provinceAgencyArea;
      condition.qlProvinceAgencyCode = qlProvinceAgencyCode;
      condition.qlAreaCode = qlAreaCode;
    }

    setFilterCondition(condition);
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value as 'progress' | 'efficiency');
  };

  const handleDimensionChange = (value: string) => {
    setDimension(value as 'day' | 'week' | 'month');
  };

  const handleStageDataUpdate = (data: any) => {
    setStageData(data);
  };

  return (
    <div className="deploy-process-monitor">
      {/* 第一个卡片：筛选表单和切换控件 */}
      <div className="filter-card">
        <div className="filter-content">
          <div className="filter-form">
            <CommonForm
              formConfig={formConfig}
              layout="inline"
              formType="edit"
              getFormInstance={(ref) => (filterFormRef.current = ref)}
              onValueChange={handleFilterChange}
            />
          </div>
          <div className="filter-controls">
            <div className="control-group">
              <span className="control-label">类型：</span>
              <Segmented
                options={[
                  { label: '进度', value: 'progress' },
                  { label: '效率', value: 'efficiency' },
                ]}
                value={activeTab}
                onChange={handleTabChange}
              />
            </div>
            <div className="control-group">
              <span className="control-label">维度：</span>
              <Segmented
                options={[
                  { label: '日', value: 'day' },
                  { label: '周', value: 'week' },
                  { label: '月', value: 'month' },
                ]}
                value={dimension}
                onChange={handleDimensionChange}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 动态显示组件 */}
      {activeTab === 'progress' ? (
        <ProgressComponent
          filterCondition={filterCondition}
          stageData={stageData}
          onStageDataUpdate={handleStageDataUpdate}
        />
      ) : (
        <EfficiencyComponent
          filterCondition={filterCondition}
          stageData={stageData}
          onStageDataUpdate={handleStageDataUpdate}
        />
      )}
    </div>
  );
};

export default React.memo(DeployProcessMonitor);
