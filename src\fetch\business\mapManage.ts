import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

// Mock数据
const mapManagePageMockData = {
  code: '0000',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 8,
    total: 75,
    countMap: {
      TOTAL: 75,
      TO_REPORT: 12,
      TO_COLLECT: 10,
      COLLECTING: 8,
      TO_DRAW: 15,
      DRAWING: 12,
      VERIFYING: 10,
      COMPLETED: 8,
    },
    list: Array.from({ length: 20 }, (_, index) => ({
      id: `160`,
      taskName: `绘制线路`,
      countryName: '北京-北京市-朝阳区',
      areaName: '华北区-朝阳片区',
      stationName: `站点${index + 1}`,
      stationNumber: `ST${(index + 1).toString().padStart(4, '0')}`,
      taskRouteType: '配送路线',
      applicateScene: '快递配送',
      vehicleTypeName: 'JD-6EF型配送车',
      startPoint: `起点${index + 1}`,
      endPoint: `终点${index + 1}`,
      roadNames: `道路${index + 1}号线`,
      duration: Math.floor(Math.random() * 120) + 30,
      totalMileage: Math.floor(Math.random() * 50) + 10,
      supplier: ['宽凳', '全道', '航天宏图', '其他'][index % 4],
      taskStatus: [
        'TO_REPORT',
        'TO_COLLECT',
        'COLLECTING',
        'TO_DRAW',
        'DRAWING',
        'VERIFYING',
        'COMPLETED',
      ][index % 7],
      taskStatusName: [
        '待提报',
        '待采图',
        '采图中',
        '待制图',
        '制图中',
        '验图中',
        '已完成',
      ][index % 7],
      estCompleteTime: '2025-08-15',
      taskSubmitTime: '2025-08-10 10:30:00',
    })),
  },
};

// 接口参数类型定义
export interface MapManagePageRequest {
  startTime?: string;
  endTime?: string;
  provinceId?: number | null;
  cityId?: number | null;
  countryId?: number | null;
  companyCode?: string;
  areaCode?: string;
  stationNumber?: string;
  taskStatus?: string;
  mapVendor?: string;
  vehicleModelName?: string;
  durationOperator?: string;
  durationValue?: number | null;
  mileageOperator?: string;
  mileageValue?: number | null;
  pageNum: number;
  pageSize: number;
}

export interface MapManageItem {
  id: string;
  taskName: string;
  countryName: string;
  areaName: string;
  stationName: string;
  stationNumber: string;
  taskRouteType: string;
  applicateScene: string;
  vehicleTypeName: string;
  startPoint: string;
  endPoint: string;
  roadNames: string;
  duration: number;
  totalMileage: number;
  supplier: string;
  taskStatus: string;
  taskStatusName: string;
  estCompleteTime: string;
  taskSubmitTime: string;
}

export interface MapManagePageResponse {
  pageNum: number;
  pageSize: number;
  pages: number;
  total: number;
  countMap: {
    TOTAL: number;
    TO_REPORT: number;
    TO_COLLECT: number;
    COLLECTING: number;
    TO_DRAW: number;
    DRAWING: number;
    VERIFYING: number;
    COMPLETED: number;
  };
  list: MapManageItem[];
}

// 领取任务接口参数类型
export interface AcceptTaskRequest {
  taskId: number;
  contactUser: string;
  contactPhone: string;
  mapVendor: string;
  estCompleteTime: string;
}

// 完成任务接口参数类型
export interface CompleteTaskRequest {
  taskId: number;
  contactUser: string;
  contactPhone: string;
  completeTime: string;
}

// 批量提交任务接口参数类型
export interface SubmitTaskListRequest {
  taskIdList: number[];
}

// 下载接口参数类型
export interface DownloadTaskRequest {
  taskId: number[];
}

// 下载接口响应类型
export interface DownloadTaskResponse {
  code: string;
  message: string;
  data: string; // s3下载地址
}

// 接口响应类型
export interface TaskOperationResponse {
  code: string;
  message: string;
}

class MapManageApi {
  // 分页查询地图任务列表
  public async getMapManagePage(params: MapManagePageRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/page',
      method: Method.POST,
      body: params,
    };
    console.log('调用分页查询地图任务接口了！！', params);

    return Promise.resolve(mapManagePageMockData);
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 领取任务
  public async acceptTask(params: AcceptTaskRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/acceptTask',
      method: Method.POST,
      body: params,
    };
    console.log('调用领取任务接口了！！', params);

    // Mock数据
    return Promise.resolve({
      code: '000',
      message: '领取任务成功',
    });
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 完成任务
  public async completeTask(params: CompleteTaskRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/completeTask',
      method: Method.POST,
      body: params,
    };
    console.log('调用完成任务接口了！！', params);

    // Mock数据
    return Promise.resolve({
      code: '0000',
      message: '完成任务成功',
    });
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 批量提交地图任务列表
  public async submitTaskList(params: SubmitTaskListRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/submitTaskList',
      method: Method.POST,
      body: params,
    };
    console.log('调用批量提交地图任务列表接口了！！', params);

    // Mock数据
    return Promise.resolve({
      code: '0000',
      message: '批量提交成功',
    });
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 下载线路文本描述
  public async downloadTaskRoute(params: DownloadTaskRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/downloadTaskRoute',
      method: Method.POST,
      body: params,
    };
    console.log('调用下载线路文本描述接口了！！', params);

    // Mock数据
    return Promise.resolve({
      code: '0000',
      message: '获取下载地址成功',
      data: 'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0%E8%AE%BE%E5%A4%87%E5%AF%BC%E5%85%A5%E6%96%87%E4%BB%B6.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250812T055309Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250812%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=230bb44e42560f6a5a121f2c1724b14827ffc9d036dfd152f9f6019a5525b3a9',
    });
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }

  // 下载txt文件
  public async downloadShpFile(params: DownloadTaskRequest): Promise<any> {
    const requestOptions = {
      path: '/k2/management/deployment/mapTask/downloadShpFile',
      method: Method.POST,
      body: params,
    };
    console.log('调用下载txt文件接口了！！', params);

    // Mock数据
    return Promise.resolve({
      code: '0000',
      message: '获取下载地址成功',
      data: 'https://rover-operation.s3.cn-north-1.jdcloud-oss.com/%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0%E8%AE%BE%E5%A4%87%E5%AF%BC%E5%85%A5%E6%96%87%E4%BB%B6.xlsx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250812T055309Z&X-Amz-SignedHeaders=host&X-Amz-Expires=86400&X-Amz-Credential=85C76AB2AB89077E8500CB6021A81EC7%2F20250812%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=230bb44e42560f6a5a121f2c1724b14827ffc9d036dfd152f9f6019a5525b3a9',
    });
    // 正式环境使用下面的代码
    // return request(requestOptions);
  }
}

export const mapManageApi = new MapManageApi();
