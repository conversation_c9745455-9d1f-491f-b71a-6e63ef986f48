import React, { useState, useEffect, useRef } from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { UnorderedListOutlined } from '@ant-design/icons';
import MetricsCard from '../MetricsCard';
import StageOverview from '../StageOverview';
import { deployProcessMonitorApi } from '@/fetch/business/deployProcessMonitor';
import { commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  efficiencyMetricsConfig,
  efficiencySearchConfig,
  efficiencyTableColumns,
  defaultHiddenColumns,
  defaultLeftFixedColumns,
  defaultRightFixedColumns,
} from '../../utils/constant';
import './index.scss';

interface EfficiencyComponentProps {
  filterCondition: any;
  stageData: any;
  onStageDataUpdate: (data: any) => void;
}

const EfficiencyComponent: React.FC<EfficiencyComponentProps> = ({
  filterCondition,
  stageData,
  onStageDataUpdate,
}) => {
  const [overviewData, setOverviewData] = useState<any>({});
  const [searchCondition, setSearchCondition] = useState<any>({
    pageNum: 1,
    pageSize: 10,
    searchForm: {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(efficiencySearchConfig);
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  // 使用useTableData钩子
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    (params) => deployProcessMonitorApi.getEfficiencyPageList({
      ...params,
      ...filterCondition,
    }),
    'efficiency-table',
    false
  );

  // 初始化下拉框数据
  useEffect(() => {
    initializeDropdownData();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (filterCondition) {
      loadOverviewData();
    }
  }, [filterCondition]);

  // 监听搜索条件变化
  useEffect(() => {
    if (Object.keys(searchCondition.searchForm).length > 0) {
      reloadTable();
    }
  }, [searchCondition]);

  const initializeDropdownData = async () => {
    try {
      const stationRes = await commonApi.getStationList();
      if (stationRes.code === HttpStatusCode.Success) {
        const newFormConfig = {
          ...searchFormConfig,
          fields: searchFormConfig.fields.map((field: any) => {
            if (field.fieldName === 'stationNumber') {
              return {
                ...field,
                options: stationRes.data || [],
              };
            }
            return field;
          }),
        };
        setSearchFormConfig(newFormConfig);
      }
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
    }
  };

  const loadOverviewData = async () => {
    try {
      const res = await deployProcessMonitorApi.getEfficiencyOverview(filterCondition);
      if (res.code === HttpStatusCode.Success) {
        setOverviewData(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('加载效率总览数据失败:', error);
      message.error('加载数据失败');
    }
  };

  const onSearchClick = (values: any) => {
    const formattedValues = { ...values };
    
    // 格式化时间字段
    if (values.expectedDeliveryTime) {
      formattedValues.expectedDeliveryTime = values.expectedDeliveryTime.format('YYYY-MM');
    }

    // 处理交付天数字段
    if (values.deliveryDuration !== undefined && values.deliveryDuration !== null) {
      formattedValues.deliveryDuration = values.deliveryDuration;
      formattedValues.deliveryDurationSymbol = values.deliveryDurationSymbol || 'gt';
    }

    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: formattedValues,
    });
  };

  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: {},
    });
  };

  // 构建指标数据
  const metricsData = efficiencyMetricsConfig.map(config => ({
    name: config.name,
    count: overviewData[config.field] || 0,
    bgPic: config.bgPic,
    unit: config.unit,
  }));

  return (
    <div className="efficiency-component">
      {/* 指标卡片 */}
      <MetricsCard metrics={metricsData} />
      
      {/* 阶段总览 */}
      <StageOverview
        title="进度效率总览"
        todayData={stageData?.todayEveryStageStatistic || {}}
        compareData={stageData?.compareEveryStageStatistic || {}}
        isProgress={false}
      />
      
      {/* 数据明细 */}
      <div className="data-detail">
        <div className="detail-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">数据明细</span>
        </div>
        
        <div ref={searchRef}>
          <CommonForm
            formConfig={searchFormConfig}
            defaultValue={{
              ...searchCondition,
              ...searchCondition.searchForm,
              fetchStationList: true,
              deliveryDurationSymbol: 'gt',
            }}
            layout="inline"
            formType="search"
            getFormInstance={(ref) => (searchFormRef.current = ref)}
            onSearchClick={onSearchClick}
            onResetClick={onResetClick}
          />
        </div>
        
        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey="efficiency-detail-table"
          columns={efficiencyTableColumns}
          loading={loading}
          rowKey="stationBaseId"
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            setSearchCondition(value);
          }}
          showColumnSetting={true}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(EfficiencyComponent);
