import React, { useState, useEffect, useRef, useMemo } from 'react';
import { message } from 'antd';
import { CommonTable, CommonForm, useTableData } from '@jd/x-coreui';
import { UnorderedListOutlined } from '@ant-design/icons';
import MetricsCard from '../MetricsCard';
import StageOverview from '../StageOverview';
import { deployProcessMonitorApi } from '@/fetch/business/deployProcessMonitor';
import { commonApi } from '@/fetch/business';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  efficiencyMetricsConfig,
  efficiencySearchConfig,
} from '../../utils/constant';
import './index.scss';

// 效率表格列配置（基础配置，不包含render函数）
const efficiencyTableColumns = [
  {
    title: '基本信息',
    children: [
      {
        title: '省市区',
        dataIndex: 'location',
        key: 'location',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '省区片区',
        dataIndex: 'area',
        key: 'area',
        width: 200,
        fixed: 'left',
        hidden: true,
      },
      {
        title: '站点名称',
        dataIndex: 'stationName',
        key: 'stationName',
        width: 150,
        fixed: 'left',
      },
      {
        title: '站点编号',
        dataIndex: 'stationNumber',
        key: 'stationNumber',
        width: 150,
        fixed: 'left',
        hidden: true,
      },
    ],
  },
  {
    title: '订单信息',
    children: [
      {
        title: '订单编号',
        dataIndex: 'orderNumber',
        key: 'orderNumber',
        width: 150,
      },
      {
        title: '车辆数',
        dataIndex: 'orderVehicleCount',
        key: 'orderVehicleCount',
        width: 100,
      },
      {
        title: '需求提报时间',
        dataIndex: 'requirementCreateTime',
        key: 'requirementCreateTime',
        width: 180,
      },
    ],
  },
  {
    title: '部署时长（天）',
    children: [
      {
        title: '需求天数',
        dataIndex: 'requirementDuration',
        key: 'requirementDuration',
        width: 100,
        overLimitField: 'requirementOverLimit',
      },
      {
        title: '订单天数',
        dataIndex: 'orderDuration',
        key: 'orderDuration',
        width: 100,
        overLimitField: 'orderOverLimit',
      },
      {
        title: '配车天数',
        dataIndex: 'supplyDuration',
        key: 'supplyDuration',
        width: 100,
        overLimitField: 'supplyOverLimit',
      },
      {
        title: '保险天数',
        dataIndex: 'insuranceDuration',
        key: 'insuranceDuration',
        width: 100,
        overLimitField: 'insuranceOverLimit',
      },
      {
        title: '发车天数',
        dataIndex: 'dispatchDuration',
        key: 'dispatchDuration',
        width: 100,
        overLimitField: 'dispatchOverLimit',
      },
      {
        title: '到车天数',
        dataIndex: 'receiveDuration',
        key: 'receiveDuration',
        width: 100,
        overLimitField: 'receiveOverLimit',
      },
      {
        title: '画图天数',
        dataIndex: 'mapDrawDuration',
        key: 'mapDrawDuration',
        width: 100,
        overLimitField: 'mapDrawOverLimit',
      },
      {
        title: '采图天数',
        dataIndex: 'mapCollectionDuration',
        key: 'mapCollectionDuration',
        width: 100,
        overLimitField: 'mapCollectionOverLimit',
      },
      {
        title: '制图天数',
        dataIndex: 'mapMakeDuration',
        key: 'mapMakeDuration',
        width: 100,
        overLimitField: 'mapMakeOverLimit',
      },
      {
        title: '验图天数',
        dataIndex: 'mapCheckDuration',
        key: 'mapCheckDuration',
        width: 100,
        overLimitField: 'mapCheckOverLimit',
      },
      {
        title: '验收天数',
        dataIndex: 'checkOrderDuration',
        key: 'checkOrderDuration',
        width: 100,
        overLimitField: 'checkOrderOverLimit',
      },
      {
        title: '交付天数',
        dataIndex: 'deliveryDuration',
        key: 'deliveryDuration',
        width: 100,
        overLimitField: 'deliveryOverLimit',
      },
    ],
  },
  {
    title: '时间信息',
    children: [
      {
        title: '交付天数',
        dataIndex: 'totalDuration',
        key: 'totalDuration',
        width: 120,
        fixed: 'right',
      },
      {
        title: '预期交付月份',
        dataIndex: 'expectedDeliveryTime',
        key: 'expectedDeliveryTime',
        width: 150,
        fixed: 'right',
      },
    ],
  },
];

interface EfficiencyComponentProps {
  filterCondition: any;
  stageData: any;
  onStageDataUpdate: (data: any) => void;
}

const EfficiencyComponent: React.FC<EfficiencyComponentProps> = ({
  filterCondition,
  stageData,
  onStageDataUpdate,
}) => {
  const [overviewData, setOverviewData] = useState<any>({});
  const [searchCondition, setSearchCondition] = useState<any>({
    pageNum: 1,
    pageSize: 10,
    searchForm: {},
  });
  const [searchFormConfig, setSearchFormConfig] = useState(
    efficiencySearchConfig,
  );
  const searchFormRef = useRef<any>(null);
  const searchRef = useRef<any>(null);

  // 渲染带颜色的数值的公共方法
  const renderColoredValue = (
    value: number,
    record: any,
    overLimitField: string,
  ) => {
    const isOverLimit = record[overLimitField];
    return (
      <span style={{ color: isOverLimit ? '#ff4d4f' : '#52c41a' }}>
        {value}
      </span>
    );
  };

  // 格式化表格列配置
  const formatColumns = useMemo(() => {
    return efficiencyTableColumns.map((group: any) => ({
      ...group,
      children: group.children?.map((col: any) => {
        // 为带有overLimitField的字段添加render函数
        if (col.overLimitField) {
          return {
            ...col,
            render: (value: number, record: any) =>
              renderColoredValue(value, record, col.overLimitField),
          };
        }
        return col;
      }),
    }));
  }, []);

  // 使用useTableData钩子
  const { tableData, loading, reloadTable } = useTableData(
    searchCondition,
    (params) =>
      deployProcessMonitorApi.getEfficiencyPageList({
        ...params,
        ...filterCondition,
      }),
    'efficiency-table',
    false,
  );

  // 初始化下拉框数据
  useEffect(() => {
    initializeDropdownData();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (filterCondition) {
      loadOverviewData();
    }
  }, [filterCondition]);

  // 监听搜索条件变化
  useEffect(() => {
    if (Object.keys(searchCondition.searchForm).length > 0) {
      reloadTable();
    }
  }, [searchCondition]);

  const initializeDropdownData = async () => {
    try {
      const stationRes = await commonApi.getStationList();
      if (stationRes.code === HttpStatusCode.Success) {
        const newFormConfig = {
          ...searchFormConfig,
          fields: searchFormConfig.fields.map((field: any) => {
            if (field.fieldName === 'stationNumber') {
              return {
                ...field,
                options: stationRes.data || [],
              };
            }
            return field;
          }),
        };
        setSearchFormConfig(newFormConfig);
      }
    } catch (error) {
      console.error('初始化下拉框数据失败:', error);
    }
  };

  const loadOverviewData = async () => {
    try {
      const res = await deployProcessMonitorApi.getEfficiencyOverview(
        filterCondition,
      );
      if (res.code === HttpStatusCode.Success) {
        setOverviewData(res.data);
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('加载效率总览数据失败:', error);
      message.error('加载数据失败');
    }
  };

  const onSearchClick = (values: any) => {
    const formattedValues = { ...values };

    // 格式化时间字段
    if (values.expectedDeliveryTime) {
      formattedValues.expectedDeliveryTime =
        values.expectedDeliveryTime.format('YYYY-MM');
    }

    // 处理交付天数字段
    if (
      values.deliveryDuration !== undefined &&
      values.deliveryDuration !== null
    ) {
      formattedValues.deliveryDuration = values.deliveryDuration;
      formattedValues.deliveryDurationSymbol =
        values.deliveryDurationSymbol || 'gt';
    }

    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: formattedValues,
    });
  };

  const onResetClick = () => {
    searchFormRef.current?.resetFields();
    setSearchCondition({
      ...searchCondition,
      pageNum: 1,
      searchForm: {},
    });
  };

  // 构建指标数据
  const metricsData = efficiencyMetricsConfig.map((config) => ({
    name: config.name,
    count: overviewData[config.field] || 0,
    bgPic: config.bgPic,
    unit: config.unit,
  }));

  return (
    <div className="efficiency-component">
      {/* 指标卡片 */}
      <MetricsCard metrics={metricsData} />

      {/* 阶段总览 */}
      <StageOverview
        title="进度效率总览"
        todayData={stageData?.todayEveryStageStatistic || {}}
        compareData={stageData?.compareEveryStageStatistic || {}}
        isProgress={false}
      />

      {/* 数据明细 */}
      <div className="data-detail">
        <div className="detail-header">
          <UnorderedListOutlined className="header-icon" />
          <span className="header-title">数据明细</span>
        </div>

        <div ref={searchRef}>
          <CommonForm
            formConfig={searchFormConfig}
            defaultValue={{
              ...searchCondition,
              ...searchCondition.searchForm,
              fetchStationList: true,
              deliveryDurationSymbol: 'gt',
            }}
            layout="inline"
            formType="search"
            getFormInstance={(ref) => (searchFormRef.current = ref)}
            onSearchClick={onSearchClick}
            onResetClick={onResetClick}
          />
        </div>

        <CommonTable
          tableListData={{
            list: (tableData as any)?.list ?? [],
            totalNumber: (tableData as any)?.total,
            totalPage: (tableData as any)?.pages,
          }}
          tableKey="efficiency-detail-table"
          columns={formatColumns}
          loading={loading}
          rowKey="stationBaseId"
          searchCondition={searchCondition}
          onPageChange={(value: any) => {
            setSearchCondition(value);
          }}
          showColumnSetting={true}
          searchRef={searchRef}
        />
      </div>
    </div>
  );
};

export default React.memo(EfficiencyComponent);
