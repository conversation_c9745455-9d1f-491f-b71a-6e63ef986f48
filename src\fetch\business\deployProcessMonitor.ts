import { request } from '@/fetch/core';
import { Method } from '@/fetch/core/constant';

// Mock数据 - 进度数据总览
const progressOverviewMock = {
  code: '0000',
  message: 'ok',
  data: {
    deployCompleteStationCount: 156,
    deployCompleteVehicleCount: 324,
    deployingStationCount: 42,
    deployingVehicleCount: 89,
  },
};

// Mock数据 - 效率数据总览
const efficiencyOverviewMock = {
  code: '0000',
  message: 'ok',
  data: {
    stationAvgDeploymentDuration: 15.5,
    vehicleAvgDeploymentDuration: 8.2,
    mapAvgDeploymentDuration: 12.3,
  },
};

// Mock数据 - 进度阶段数据
const progressStageDataMock = {
  code: '0000',
  message: 'ok',
  data: {
    todayEveryStageStatistic: {
      requirementCount: 25,
      orderCount: 18,
      supplyCount: 12,
      insuranceCount: 15,
      dispatchCount: 8,
      receiveCount: 6,
      mapDrawCount: 10,
      mapCollectionCount: 7,
      mapMakeCount: 5,
      mapCheckCount: 4,
      checkOrderCount: 3,
      deliveryCount: 2,
    },
    compareEveryStageStatistic: {
      requirementCount: 22,
      orderCount: 20,
      supplyCount: 15,
      insuranceCount: 12,
      dispatchCount: 10,
      receiveCount: 8,
      mapDrawCount: 12,
      mapCollectionCount: 9,
      mapMakeCount: 7,
      mapCheckCount: 6,
      checkOrderCount: 5,
      deliveryCount: 4,
    },
  },
};

// Mock数据 - 效率阶段数据
const efficiencyStageDataMock = {
  code: '0000',
  message: 'ok',
  data: {
    todayEveryStageStatistic: {
      requirementDuration: 2.5,
      orderDuration: 1.8,
      supplyDuration: 3.2,
      insuranceDuration: 1.5,
      dispatchDuration: 2.1,
      receiveDuration: 1.2,
      mapDrawDuration: 4.5,
      mapCollectionDuration: 3.8,
      mapMakeDuration: 5.2,
      mapCheckDuration: 2.8,
      checkOrderDuration: 1.9,
      deliveryDuration: 1.1,
    },
    compareEveryStageStatistic: {
      requirementDuration: 2.8,
      orderDuration: 2.1,
      supplyDuration: 3.5,
      insuranceDuration: 1.8,
      dispatchDuration: 2.4,
      receiveDuration: 1.5,
      mapDrawDuration: 4.8,
      mapCollectionDuration: 4.1,
      mapMakeDuration: 5.5,
      mapCheckDuration: 3.1,
      checkOrderDuration: 2.2,
      deliveryDuration: 1.4,
    },
  },
};

// Mock数据 - 进度明细列表
const progressPageListMock = {
  code: '0000',
  message: 'ok',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 5,
    total: 45,
    list: [
      {
        stationBaseId: 1,
        stationName: '北京朝阳站',
        orderVehicleCount: 5,
        requirementCreateTime: '2024-01-15 10:30:00',
        orderNumber: 'ORD202401001',
        location: '北京-北京市-朝阳区',
        area: '北京省区-北京北区',
        expectedDeliveryTime: '2024-06-01',
        deliveryCompleteTime: '2024-06-15',
        requirementStatus: 1,
        orderStatus: 1,
        supplyStatus: 1,
        insuranceStatus: 1,
        dispatchStatus: 0,
        receiveStatus: 0,
        mapDrawStatus: 0,
        mapCollectionStatus: 0,
        mapMakeStatus: 0,
        mapCheckStatus: 0,
        checkOrderStatus: 0,
        deliveryStatus: 0,
        expectedReceiveVehicleTime: '2024-05-20',
        expectedMapCollectionCompleteTime: '2024-05-25',
        expectedMapMakeCompleteTime: '2024-05-30',
      },
      {
        stationBaseId: 2,
        stationName: '上海浦东站',
        orderVehicleCount: 3,
        requirementCreateTime: '2024-01-16 09:15:00',
        orderNumber: 'ORD202401002',
        location: '上海-上海市-浦东新区',
        area: '上海省区-上海东区',
        expectedDeliveryTime: '2024-07-01',
        deliveryCompleteTime: null,
        requirementStatus: 1,
        orderStatus: 1,
        supplyStatus: 1,
        insuranceStatus: 1,
        dispatchStatus: 1,
        receiveStatus: 1,
        mapDrawStatus: 0,
        mapCollectionStatus: 0,
        mapMakeStatus: 0,
        mapCheckStatus: 0,
        checkOrderStatus: 0,
        deliveryStatus: 0,
        expectedReceiveVehicleTime: '2024-06-20',
        expectedMapCollectionCompleteTime: '2024-06-25',
        expectedMapMakeCompleteTime: '2024-06-30',
      },
    ],
  },
};

// Mock数据 - 效率明细列表
const efficiencyPageListMock = {
  code: '0000',
  message: 'ok',
  data: {
    pageNum: 1,
    pageSize: 10,
    pages: 3,
    total: 28,
    list: [
      {
        stationBaseId: 1,
        stationName: '北京朝阳站',
        orderVehicleCount: 5,
        requirementCreateTime: '2024-01-15 10:30:00',
        orderNumber: 'ORD202401001',
        location: '北京-北京市-朝阳区',
        area: '北京省区-北京北区',
        expectedDeliveryTime: '2024-06-01',
        totalDuration: 45.5,
        requirementDuration: 2.5,
        requirementOverLimit: 0,
        orderDuration: 1.8,
        orderOverLimit: 0,
        supplyDuration: 3.2,
        supplyOverLimit: 1,
        insuranceDuration: 1.5,
        insuranceOverLimit: 0,
        dispatchDuration: 2.1,
        dispatchOverLimit: 0,
        receiveDuration: 1.2,
        receiveOverLimit: 0,
        mapDrawDuration: 4.5,
        mapDrawOverLimit: 1,
        mapCollectionDuration: 3.8,
        mapCollectionOverLimit: 0,
        mapMakeDuration: 5.2,
        mapMakeOverLimit: 1,
        mapCheckDuration: 2.8,
        mapCheckOverLimit: 0,
        checkOrderDuration: 1.9,
        checkOrderOverLimit: 0,
        deliveryDuration: 1.1,
        deliveryOverLimit: 0,
      },
      {
        stationBaseId: 2,
        stationName: '上海浦东站',
        orderVehicleCount: 3,
        requirementCreateTime: '2024-01-16 09:15:00',
        orderNumber: 'ORD202401002',
        location: '上海-上海市-浦东新区',
        area: '上海省区-上海东区',
        expectedDeliveryTime: '2024-07-01',
        totalDuration: 38.2,
        requirementDuration: 2.1,
        requirementOverLimit: 0,
        orderDuration: 1.5,
        orderOverLimit: 0,
        supplyDuration: 2.8,
        supplyOverLimit: 0,
        insuranceDuration: 1.2,
        insuranceOverLimit: 0,
        dispatchDuration: 1.8,
        dispatchOverLimit: 0,
        receiveDuration: 1.0,
        receiveOverLimit: 0,
        mapDrawDuration: 4.2,
        mapDrawOverLimit: 0,
        mapCollectionDuration: 3.5,
        mapCollectionOverLimit: 0,
        mapMakeDuration: 4.8,
        mapMakeOverLimit: 0,
        mapCheckDuration: 2.5,
        mapCheckOverLimit: 0,
        checkOrderDuration: 1.6,
        checkOrderOverLimit: 0,
        deliveryDuration: 0.9,
        deliveryOverLimit: 0,
      },
    ],
  },
};

// 接口参数类型定义
export interface ProgressOverviewRequest {
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
}

export interface EfficiencyOverviewRequest {
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
}

export interface ProgressStageRequest {
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
  dimension: 'day' | 'week' | 'month';
}

export interface EfficiencyStageRequest {
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
  dimension: 'day' | 'week' | 'month';
}

export interface ProgressPageRequest {
  pageNum: number;
  pageSize: number;
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
  deploymentStage?: string;
  expectedDeliveryTime?: string;
  deliveryCompleteStartTime?: string;
  deliveryCompleteEndTime?: string;
  stationNumber?: string;
  orderNumber?: string;
}

export interface EfficiencyPageRequest {
  pageNum: number;
  pageSize: number;
  provinceId?: number;
  cityId?: number;
  countyId?: number;
  qlProvinceAgencyCode?: string;
  qlAreaCode?: string;
  expectedDeliveryTime?: string;
  deliveryDuration?: number;
  deliveryDurationSymbol?: 'gt' | 'lt' | 'eq';
  stationNumber?: string;
  orderNumber?: string;
}

class DeployProcessMonitorApi {
  // 查询部署进度数据总览
  public async getProgressOverview(
    params: ProgressOverviewRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_progress_overview',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署进度数据总览接口了！！', params);
    return Promise.resolve(progressOverviewMock);
    return request(requestOptions);
  }

  // 查询部署效率数据总览
  public async getEfficiencyOverview(
    params: EfficiencyOverviewRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_efficiency_overview',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署效率数据总览接口了！！', params);
    return Promise.resolve(efficiencyOverviewMock);
    return request(requestOptions);
  }

  // 查询部署进度阶段数据
  public async getProgressEveryStage(
    params: ProgressStageRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_progress_every_stage',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署进度阶段数据接口了！！', params);
    return Promise.resolve(progressStageDataMock);
    return request(requestOptions);
  }

  // 查询部署效率阶段数据
  public async getEfficiencyEveryStage(
    params: EfficiencyStageRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_efficiency_every_stage',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署效率阶段数据接口了！！', params);
    return Promise.resolve(efficiencyStageDataMock);
    return request(requestOptions);
  }

  // 查询部署进度明细数据
  public async getProgressPageList(params: ProgressPageRequest): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_progress_page_list',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署进度明细数据接口了！！', params);
    return Promise.resolve(progressPageListMock);
    return request(requestOptions);
  }

  // 查询部署效率明细数据
  public async getEfficiencyPageList(
    params: EfficiencyPageRequest,
  ): Promise<any> {
    const requestOptions: RequestOptions = {
      path: '/datacenter/web/deployment/get_efficiency_page_list',
      method: Method.POST,
      body: params,
    };
    console.log('调用查询部署效率明细数据接口了！！', params);
    return Promise.resolve(efficiencyPageListMock);
    return request(requestOptions);
  }
}

export const deployProcessMonitorApi = new DeployProcessMonitorApi();
