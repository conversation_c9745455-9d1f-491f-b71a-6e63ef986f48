.stage-overview {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;

  .stage-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .header-icon {
      font-size: 18px;
      color: #1890ff;
      margin-right: 8px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .stage-category {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .category-header {
      background: #eff4ff;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 16px;
    }

    .stage-items {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .stage-item {
        flex: 1;
        min-width: 120px;
        height: 100px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 8px;
        position: relative;
        transition: all 0.3s ease;

        &.clickable {
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }

        &.active {
          transform: scale(1.05);
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
        }

        .stage-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          color: #fff;
          width: 100%;

          .stage-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .stage-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            justify-content: center;

            .unit {
              font-size: 12px;
              font-weight: normal;
              margin-left: 2px;
            }
          }

          .change-indicator {
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }

            &.neutral {
              color: #d9d9d9;
            }
          }
        }
      }
    }
  }
}
