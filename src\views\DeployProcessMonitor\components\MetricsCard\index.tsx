import React from 'react';
import './index.scss';

interface MetricItem {
  name: string;
  count: number;
  bgPic: string;
  unit?: string;
}

interface MetricsCardProps {
  metrics: MetricItem[];
}

const MetricsCard: React.FC<MetricsCardProps> = ({ metrics }) => {
  return (
    <div className="metrics-card">
      {metrics.map((metric, index) => (
        <div
          key={index}
          className="metric-item"
          style={{
            width: `calc(100% / ${metrics.length})`,
            backgroundImage: `url(${metric.bgPic})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        >
          <div className="metric-content">
            <div className="metric-count">
              {metric.count}
              {metric.unit && <span className="metric-unit">{metric.unit}</span>}
            </div>
            <div className="metric-name">{metric.name}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default React.memo(MetricsCard);
